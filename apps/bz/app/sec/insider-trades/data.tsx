import { safeTimeout } from '@benzinga/safe-await';
import { getGlobalSession } from '../../../pages/api/session';
import { InsiderTradesManager, InsidersRequestOptions } from '@benzinga/insider-trades-manager';

export const getInsiderTradesDefault = async () => {
  const session = await getGlobalSession();
  const presets = await safeTimeout(session.getManager(InsiderTradesManager).getDefaultInsiderTrade(), 1000);
  if (presets.err) {
    console.error('Error fetching insider trades preset:', presets.err);
    return [];
  }
  return presets.ok;
};

export const getInsiderTradesPreset = async options => {
  const session = await getGlobalSession();
  const results = await safeTimeout(session.getManager(InsiderTradesManager).getPresetInsiderTrade(options), 1000);
  if (results.err) {
    console.error('Error fetching insider trades:', results.err);
    return results.ok;
  }
  return results.ok;
};

export const getInsiderTradesPresets = async () => {
  const session = await getGlobalSession();
  const results = await safeTimeout(session.getManager(InsiderTradesManager).getInsiderTradesPresets(), 1000);
  if (results.err) {
    console.error('Error fetching insider trades presets:', results.err);
    return results.ok;
  }
  return results.ok.presets;
};

export const getInsiderTradesSearch = async (options: InsidersRequestOptions) => {
  const session = await getGlobalSession();

  if (options?.preset_name) {
    const results = await safeTimeout(session.getManager(InsiderTradesManager).getPresetInsiderTrade(options), 1000);
    if (results.err) {
      console.error('Error fetching insider trades search:', results.err);
      return results.ok;
    }
    return results.ok;
  }

  const results = await session.getManager(InsiderTradesManager).getInsiderTradesV2(options);
  if (results.err) {
    console.error('Error fetching insider trades search:', results.err);
  }
  return results.ok;
};

export const getInsiderTradesTextSearch = async options => {
  const session = await getGlobalSession();
  const results = await safeTimeout(session.getManager(InsiderTradesManager).getInsiderTradesTextSearch(options), 1000);
  if (results.err) {
    console.error('Error fetching insider trades autocomplete:', results.err);
  }
  return results.ok;
};

export const getInsiderTradesNetWorth = async cik => {
  const session = await getGlobalSession();

  const results = await safeTimeout(session.getManager(InsiderTradesManager).getInsiderTradesNetWorth(cik), 1000);
  if (results.err) {
    console.error('Error fetching insider trades autocomplete:', results.err);
  }
  return results.ok;
};
