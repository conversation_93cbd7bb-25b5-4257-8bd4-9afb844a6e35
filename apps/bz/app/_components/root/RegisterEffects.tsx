'use client';
import { useEffect } from 'react';
import Hooks from '@benzinga/hooks';
import Modal from 'react-modal';
// eslint-disable-next-line
import { Authentication, AuthenticationManager } from '@benzinga/session';
import { usePathname, useSearchParams } from 'next/navigation';
// eslint-disable-next-line
import { getGlobalSession } from 'apps/bz/pages/api/session';
import { logCommitHash, logFriendlyBenzingaMessageToDevelopers, logReleaseVersion } from '@benzinga/utils';
import { CoralogixRumLoader } from '@benzinga/coralogix-monitoring';
import { GoogleTagAnalytics } from '@benzinga/analytics';
import { TrackingManager } from '@benzinga/tracking-manager';
import { safeDelay } from '@benzinga/safe-await';
import { useIsUserPaywalled } from '@benzinga/user-context';

function RegisterEffects({ pageProps }: { pageProps?: Record<string, any> }) {
  const session = getGlobalSession();
  const pathname = usePathname();
  const searchParams = useSearchParams();
  const authManager = session.getManager(AuthenticationManager);
  const paywall = useIsUserPaywalled();

  useEffect(() => {
    if (document.readyState === 'complete') {
      Modal.setAppElement('#app-modal-element');
    } else {
      window.addEventListener('load', () => Modal.setAppElement('#app-modal-element'));
    }

    session.getManager(AuthenticationManager).getAuthSession();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [pathname]);

  useEffect(() => {
    const isCalendarPage = window.location.pathname.includes('/calendars/');
    if (isCalendarPage) {
      (async () => {
        await import('@ag-grid-community/styles/ag-grid.css');
        await import('@ag-grid-community/styles/ag-theme-alpine.css');
        await import('tippy.js/dist/tippy.css'); // Used by FDA Calendar
      })();
    }
  }, []);

  Hooks.useEffectReadyState(() => {
    if (!window.gtagAnalytics) {
      GoogleTagAnalytics.initialize({
        logger: true,
        measurementId: process.env.GOOGLE_TAG_MANAGER_MEASUREMENT_ID ?? '',
      });
    }
  }, ['interactive', 'complete']);

  Hooks.useEffectDidMount(() => {
    logCommitHash();
    logReleaseVersion();
    logFriendlyBenzingaMessageToDevelopers();
  });

  useEffect(() => {
    if (!authManager.isLoggedIn() && !pageProps?.embeddedWidget && !paywall?.active && !paywall?.isLoading) {
      const callback = async (auth: Authentication) => {
        if (auth?.user?.accessType !== 'anonymous') {
          try {
            session.getManager(TrackingManager).trackAuthEvent('login', { auth_type: 'google-one-tap' });
            await safeDelay(500);
          } catch (e) {
            console.error('Error tracking one-tap login', e);
          }
          if (window.location?.pathname?.includes('/login') && searchParams?.get('iframe')) {
            window.parent.postMessage(
              { next: searchParams?.get('redirect') ?? searchParams?.get('next') ?? undefined, type: `login_succeed` },
              '*',
            );
          }
        }
      };
      authManager.showGoogleOneTap(callback);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [authManager, paywall, pageProps]);

  Hooks.useEffectDidMount(() => {
    // Hack to prevent jumping if the last scroll position was at LazyLoaded block
    if (window.history) {
      window.history.scrollRestoration = 'manual';
    }
  });

  useEffect(() => {
    (async () => {
      const user = (await session.getAuthenticationManager().getAuthSession()).ok?.user;
      CoralogixRumLoader(user);
    })();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  return null;
}

export default RegisterEffects;
