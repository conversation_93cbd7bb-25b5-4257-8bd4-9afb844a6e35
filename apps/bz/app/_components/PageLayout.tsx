import React from 'react';
import { AuthenticationManager } from '@benzinga/session';
import { BenzingaEdgeProvider } from '@benzinga/edge';
import { LoadingScreen } from '@benzinga/core-ui';

import AdditionalScripts from './root/AdditionalScripts';
import RootFooter from './root/RootFooter';
import RootHeader from './root/RootHeader';
import RootTracker from './root/RootTrackers';
import RegisterEffects from './root/RegisterEffects';
import { getGlobalSession } from '../../pages/api/session';

export default function PageLayout({ children, pageProps }: { children: React.ReactNode; pageProps?: any }) {
  const session = getGlobalSession();
  const authManager = session.getManager(AuthenticationManager);
  const useSuspense = true;
  const isLoggedIn = authManager.isLoggedIn();
  const isSSR = pageProps?.isSSR ?? false;

  return (
    <>
      <RegisterEffects pageProps={pageProps} />
      <RootHeader headerProps={pageProps?.headerProps} isLoggedIn={isLoggedIn} />
      <RootTracker pageProps={pageProps} />
      <AdditionalScripts pageProps={pageProps} />
      <BenzingaEdgeProvider initialTargeting={pageProps?.pageTargeting}>
        {isSSR ? <>{children}</> : <React.Suspense fallback={<LoadingScreen />}>{children}</React.Suspense>}
      </BenzingaEdgeProvider>
      <RootFooter pageProps={pageProps} />
    </>
  );
}
