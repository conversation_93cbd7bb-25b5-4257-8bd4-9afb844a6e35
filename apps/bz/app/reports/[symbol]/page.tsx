import React from 'react';
import Image from 'next/image';
import { notFound } from 'next/navigation';
import PageLayout from '../../_components/PageLayout';
import { getReportTickerPageProps } from './data';

import { UpdatedBadge } from '../../reports/components/UpdatedBadge';
import { Report } from './components/Report';
import { Stats } from './components/Stats';
import { ReportPaywall } from './components/ReportPaywall';

import styles from './report.module.scss';

export async function generateMetadata(props) {
  const params = await props.params;
  const symbol = params?.symbol ? params.symbol.toUpperCase() : '';
  const data = await getReportTickerPageProps(symbol);
  return data.props.metaProps;
}

export default async function ReportTickerPage(props) {
  const params = await props.params;
  const symbol = params?.symbol ? params.symbol.toUpperCase() : '';
  const data = await getReportTickerPageProps(symbol);

  if (data?.props?.notSupported) {
    return notFound();
  }

  const { logoUrl, stats } = data.props;
  return (
    <PageLayout pageProps={{ isSSR: true }}>
      <>
        <div className={styles.header}>
          <Image
            alt={'reports hero background'}
            fetchPriority="high"
            layout="fill"
            objectFit="cover"
            src="/next-assets/images/headerbg.jpg"
          />
          <UpdatedBadge date={stats?.updatedDate ?? null} />
          <div className={styles.headerContent}>
            <h1>Stock Analysis</h1>
            <Stats logoUrl={logoUrl} stats={stats} symbol={symbol} />
          </div>
        </div>
        <div className={styles.contentWrapper}>
          <Report {...data.props} />
          <ReportPaywall />
        </div>
      </>
    </PageLayout>
  );
}
