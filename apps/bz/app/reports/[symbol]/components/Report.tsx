import React from 'react';

import {
  GeneralSection,
  GraphSection,
  GradeSection,
  PeersSection,
  GrowthSection,
  RelativeVolumeSection,
  DividendSection,
  HealthSection,
  PastSection,
  OptionsSection,
  FinancialSection,
} from './sections';
import { GetReportDataResponse } from '@benzinga/stock-reports-manager';
import { Candle } from '../../../../src/components/Report';
import { Stats } from '../data';

type Props = {
  candles5Year?: Candle[];
  candles60Days?: Candle[];
  symbol: string;
  reportData?: GetReportDataResponse | null;
  stats?: Stats;
};

export const Report = (props: Props) => {
  const { candles5Year, candles60Days, symbol, reportData } = props;
  const hasGeneralSection = reportData?.section_status?.general;
  const companyName = props?.stats?.companyInfo?.company_name ?? '';

  const availableSections: string[] = [];
  for (const section in reportData?.section_status) {
    if (reportData?.section_status[section]) {
      availableSections.push(section);
    }
  }
  const sections = [
    'top_peers',
    'growth',
    'relative_value',
    'health',
    'dividend',
    'past',
    'options_sentiment',
    'financials',
  ].filter(section => availableSections.includes(section));

  return (
    <div className={`relative flex flex-col items-center w-full p-2 lg:p-12 report text-bzblue-800 bg-bzblue-200`}>
      {hasGeneralSection && <GeneralSection data={reportData.data} index={1} />}
      {candles60Days && (
        <GraphSection candles={candles60Days} index={hasGeneralSection ? 2 : 1} name={companyName} symbol={symbol} />
      )}
      {reportData?.data?.scores && (
        <GradeSection
          index={3 - (hasGeneralSection ? 0 : 1) - (candles60Days ? 0 : 1)}
          data={reportData?.data}
          name={companyName}
          symbol={symbol}
        />
      )}
      {Array.isArray(sections) &&
        reportData?.data &&
        sections.map((section, index) => {
          const sectionIndex =
            index + (hasGeneralSection ? 1 : 0) + (candles60Days ? 1 : 0) + (reportData.data.scores ? 1 : 0);
          if (section === 'top_peers') {
            return <PeersSection key={index} index={sectionIndex} data={reportData.data} />;
          }
          if (section === 'growth') {
            return (
              <GrowthSection
                key={index}
                index={sectionIndex}
                data={reportData.data}
                candles={candles5Year}
                name={companyName}
                symbol={symbol}
              />
            );
          }
          if (section === 'relative_value') {
            return <RelativeVolumeSection key={index} index={sectionIndex} data={reportData.data} symbol={symbol} />;
          }
          if (section === 'health') {
            return (
              <HealthSection
                key={index}
                index={sectionIndex}
                data={reportData.data}
                symbol={symbol}
                name={companyName}
              />
            );
          }
          if (section === 'dividend') {
            return (
              <DividendSection
                key={index}
                index={sectionIndex}
                data={reportData.data}
                symbol={symbol}
                name={companyName}
              />
            );
          }
          if (section === 'past') {
            return (
              <PastSection
                key={index}
                index={sectionIndex}
                data={reportData.data}
                candles={candles5Year}
                name={companyName}
                symbol={symbol}
              />
            );
          }
          if (section === 'options_sentiment') {
            return (
              <OptionsSection
                key={index}
                index={sectionIndex}
                data={reportData.data}
                symbol={symbol}
                name={companyName}
              />
            );
          }
          if (section === 'financials') {
            return <FinancialSection key={index} index={sectionIndex} data={reportData.data} symbol={symbol} />;
          }
          return null;
        })}
    </div>
  );
};

export default Report;
