import React, { useState } from 'react';

import styled from '@benzinga/themetron';
import { Pagination } from '@benzinga/core-ui';
import { getGlobalSession } from '../../api/session';
import { injectLogos } from '@benzinga/calendars';

import { GovernmentMember, GovTradesManager, SecurityTrader, MemberTrade, Security } from '@benzinga/gov-trades';
import {
  GovernmentTradesHeader,
  initialSortOps,
  MemberCard,
  TradeRow,
  TraderRow,
  processSortingMembers,
  SortCongressMembers,
  GovSearchBarV2,
} from '../../../src/components/GovTrades';
import { GovTradeOptionI } from '../../../src/components/GovTrades/SortCongressMembers';
import { Meta, MetaProps, PageType } from '@benzinga/seo';
import { SessionContext } from '@benzinga/session-context';
import { usePermission } from '@benzinga/user-context';
import { AiOutlineLock } from 'react-icons/ai';
import { TrackingManager } from '@benzinga/tracking-manager';

const BzEdgeCTA = React.lazy(() => import('@benzinga/ads').then(module => ({ default: module.BzEdgeCTA })));

interface GovMemberProps {
  metaProps: MetaProps;
  membersData: GovTradeOptionI[];
  initialMembers: GovernmentMember[];
  topGainers: SecurityTrader[];
  topLosers: SecurityTrader[];
  topTrades: MemberTrade[];
  securitiesList: Security[];
}

const metaProps: MetaProps = {
  canonical: 'https://www.benzinga.com/gov-trades/members',
  description: `Track US Congress members' stock trades in real time. Search for lawmakers, view the latest trades, volumes, and transparency updates from both the House and Senate.`,
  image:
    'https://cdn.benzinga.com/files/imagecache/bz2_opengraph_meta_image_400x300/sites/all/themes/bz2/images/bz-icon.png',
  pageType: PageType.Tool,
  title: "US Congress Members' Stock Trades - Real-Time Lawmaker Trading Tracker",
};

const GovMembersPage: React.FC<GovMemberProps> = props => {
  const { metaProps: meta } = props;
  const [activeTab, setActiveTab] = React.useState('gainers');
  const [members, setMembers] = useState(props?.initialMembers ?? []);
  const [membersArr, setMembersArr] = useState(props?.membersData?.[0]?.members ?? []);
  const [sortedMembers, setSortedMembers] = useState(props?.membersData ?? initialSortOps);
  const [losers, setLosers] = useState(props?.topLosers ?? []);
  const [gainers, setGainers] = useState(props?.topGainers ?? []);
  const [trades, setTrades] = useState(props?.topTrades ?? []);
  const hasPermission = usePermission('com/read', 'unlimited-calendars');
  const pageSize = 10;

  const handleTabClick = (tab: string) => {
    setActiveTab(tab);
  };
  const [page, setPage] = useState(1);
  const [tradersLimit, setTradersLimit] = useState(5);
  const [topTradesLimit, setTopTradesLimit] = useState(5);
  const session = React.useContext(SessionContext);

  React.useEffect(() => {
    session?.getManager(TrackingManager).setMeta(meta);
  }, [session, meta]);

  React.useEffect(() => {
    const getPropsClientSide = async () => {
      const overview = await session.getManager(GovTradesManager).getMembers();
      const topTrades = await injectLogos(overview?.ok?.top_5_trades ?? []);
      const membersSorted = processSortingMembers(overview?.ok?.congressional_members ?? []);

      setMembers(membersSorted[0]?.members?.slice(0, pageSize) ?? []);
      setMembersArr(membersSorted[0]?.members ?? []);
      setSortedMembers(membersSorted);
      setTrades(topTrades);
      setGainers(overview?.ok?.top_5_traders?.gainers ?? []);
      setLosers(overview?.ok?.top_5_traders?.losers ?? []);
    };

    if (!props?.membersData || props?.membersData?.length === 0) {
      getPropsClientSide();
    }
  }, [session, props?.membersData]);

  const handlePageChange = ({ nextPage, pageSize }) => {
    setPage(nextPage);
    const startIdx = (nextPage - 1) * pageSize;
    const endIdx = startIdx + pageSize;
    const members = membersArr.slice(startIdx, endIdx);
    setMembers(members);
  };

  const updateMembersList = sortedMembers => {
    setMembersArr(sortedMembers);
    setMembers(sortedMembers.slice(0, pageSize));
    setPage(1);
  };

  const moreToShow = () => {
    const limit = activeTab === 'gainers' ? props?.topGainers?.length : props?.topLosers?.length;
    return typeof limit === 'number' && limit > tradersLimit;
  };

  return (
    <GovernmentTradesWrapper>
      {meta && <Meta {...meta} canonical={meta.canonical} />}
      <GovernmentTradesHeader />
      <div className="main-body flex flex-col-reverse lg:flex-row gap-4">
        <div className="w-full lg:w-9/12">
          <div className="mb-4">
            <h1 className="mb-2">US Congress Members Stock Trading Tracker</h1>
            <p className="text-base text-bzblue-900/80">
              Instantly view and filter the latest stock trade disclosures from US Senators and Representatives. Stay
              updated on recent transactions, trade volumes, and lawmaker activity, sorted by date and volume.
            </p>
          </div>
          <div className="flex flex-col items-end md:flex-row md:items-center gap-4">
            <div className="flex-1 w-full">
              <GovSearchBarV2 members={membersArr} securities={props?.securitiesList ?? []} />
            </div>
          </div>
          {/* All members cards here */}
          <div className={`relative w-full gap-4 mt-4 grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5`}>
            {members.map((member, index) => (
              <MemberCard key={index} {...member} />
            ))}
            {!hasPermission && page > 2 && (
              <div className="banner-wrapper">
                <BzEdgeCTA type="gov-trades" />
              </div>
            )}
          </div>
          <div className="my-4 flex flex-col md:flex-row items-center justify-between gap-4">
            <Pagination
              buttonClassName="pagination-button"
              defaultPage={page}
              onPageChanged={handlePageChange}
              pageSize={pageSize}
              totalItems={membersArr.length}
            />
            <SortCongressMembers
              locked={!hasPermission}
              membersData={sortedMembers}
              updateMembersList={updateMembersList}
            />
          </div>
        </div>
        <div className="w-full lg:w-3/12 flex flex-col md:flex-row lg:flex-col gap-4">
          {/* Top Trades */}
          <div className="card">
            <div className="card-header">Top Trades</div>
            <div className="card-body">
              {trades.slice(0, topTradesLimit).map((trade, index) => (
                <TradeRow key={index} {...trade} />
              ))}
            </div>
            <button
              className="show-more"
              disabled={!hasPermission || topTradesLimit > trades.length}
              onClick={() => setTopTradesLimit(prev => prev + 5)}
            >
              {!hasPermission && <AiOutlineLock size={16} />}
              {topTradesLimit < trades.length ? 'Show More' : 'Showing all'}
            </button>
          </div>
          {/* Top Traders */}
          <div className="card">
            <div className="card-header">Top Traders</div>
            <div className="tab-group">
              <button
                className={`tab ${activeTab === 'gainers' ? 'active' : ''}`}
                onClick={() => handleTabClick('gainers')}
              >
                Gainers
              </button>
              <button
                className={`tab ${activeTab === 'losers' ? 'active' : ''}`}
                onClick={() => handleTabClick('losers')}
              >
                Losers
              </button>
            </div>
            <div className="card-body">
              {activeTab === 'gainers' ? (
                <>
                  {gainers.slice(0, tradersLimit).map((trade, index) => (
                    <TraderRow key={index} {...trade} type={'gainer'} />
                  ))}
                </>
              ) : (
                <>
                  {losers.slice(0, tradersLimit).map((trade, index) => (
                    <TraderRow key={index} {...trade} type={'loser'} />
                  ))}
                </>
              )}
            </div>
            <button
              className="show-more"
              disabled={!hasPermission || !moreToShow()}
              onClick={() => setTradersLimit(prev => prev + 5)}
            >
              {!hasPermission && <AiOutlineLock size={16} />}
              {moreToShow() ? 'Show More' : 'Showing all'}
            </button>
          </div>
        </div>
      </div>
    </GovernmentTradesWrapper>
  );
};

export async function getServerSideProps() {
  try {
    const session = getGlobalSession();
    const overview = await session.getManager(GovTradesManager).getMembers();
    const topTrades = await injectLogos(overview?.ok?.top_5_trades ?? []);
    const membersSorted = processSortingMembers(overview?.ok?.congressional_members ?? []);
    const securities = await session.getManager(GovTradesManager).getSecuritiesList();

    return {
      props: {
        initialMembers: membersSorted[0]?.members?.slice(0, 10) ?? [],
        membersData: membersSorted,
        metaProps,
        topGainers: overview.ok?.top_5_traders?.gainers ?? [],
        topLosers: overview.ok?.top_5_traders?.losers ?? [],
        topTrades: topTrades ?? [],
        securitiesList: securities?.ok ?? [],
      },
    };
  } catch (err) {
    console.log(err);
    return {
      props: {
        initialMembers: [],
        membersData: [],
        metaProps,
        topGainers: [],
        topLosers: [],
        topTrades: [],
        securitiesList: [],
      },
    };
  }
}

export default GovMembersPage;

const GovernmentTradesWrapper = styled.div`
  font-family: 'Manrope', 'Inter', 'Open Sans', sans-serif;
  background: #e1ebfa;

  .card {
    padding-top: 16px;
    border-radius: 12px;
    background: #fff;
    display: flex;
    flex-direction: column;
    gap: 8px;

    .card-header {
      text-align: center;
      font-size: 18px;
      font-weight: 700;
      text-transform: uppercase;
      color: #192940;
    }

    .tab-group {
      display: grid;
      grid-template-columns: 1fr 1fr;
      gap: 8px;
      padding: 0 8px;
      overflow-x: auto;

      .tab {
        cursor: pointer;
        display: flex;
        height: 28px;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        flex: 1 0 0;
        border-radius: 4px;
        color: #5b7292;
        font-size: 14px;
        text-transform: uppercase;
        font-weight: 700;
        border: 1px solid #ecf3ff;

        &.active {
          color: #3f83f8;
          background: #ecf3ff;
        }
      }
    }

    .card-body {
      display: flex;
      flex-direction: column;
      gap: 8px;
      padding: 0 8px;
      max-height: 280px;
      overflow-y: scroll;
    }

    .show-more {
      color: #3f83f8;
      text-transform: uppercase;
      text-align: center;
      font-size: 16px;
      font-weight: 700;
      line-height: 20px;
      padding: 8px 24px;
      display: flex;
      justify-content: center;
      align-items: center;
      width: 100%;
      background: #f2f8ff;
      border-bottom-right-radius: 12px;
      border-bottom-left-radius: 12px;
    }
  }

  .main-body {
    padding: 40px;
    max-width: 1400px;
    margin: 0 auto;
    @media (max-width: 800px) {
      padding: 1rem;
    }

    .banner-wrapper {
      display: flex;
      align-items: center;
      justify-content: center;
      position: absolute;
      backdrop-filter: blur(10px);
      width: 100%;
      height: 100%;
      @media (max-width: 800px) {
        padding-top: 2rem;
        align-items: flex-start;
      }
    }

    .pagination-button {
      border-radius: 4px;
      color: #5b7292 !important;
      &:hover {
        border: 1px solid white !important;
      }
      &.active {
        background: white;
        border: none !important;
      }
    }
  }
`;
