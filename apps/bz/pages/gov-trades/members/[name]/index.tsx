import React from 'react';
import styled from '@benzinga/themetron';
import classNames from 'classnames';

import { numberShorthand } from '@benzinga/utils';
import { injectLogos } from '@benzinga/calendars';
import { getGlobalSession } from '../../../../pages/api/session';
import { TrackingManager } from '@benzinga/tracking-manager';

import { GovTradesManager } from '@benzinga/gov-trades';
import { DualLineChart } from '../../../../src/components/GovTrades/Charts';
import {
  GovernmentTradesHeader,
  GovernmentTable,
  GovSearchBarV2,
  MemberRecentColumnsDef,
  MemberTopColumnsDef,
  MemberLargestColumnsDef,
  TradeRow,
  congressParties,
} from '../../../../src/components/GovTrades';
import { useRouter } from 'next/router';
import { Meta, PageType } from '@benzinga/seo';
import { SessionContext } from '@benzinga/session-context';
import { IoPersonSharp } from 'react-icons/io5';
import { useIsUserLoggedIn } from '@benzinga/user-context';
import { AuthContainer } from '@benzinga/auth-ui';
import { NoFirstRender } from '@benzinga/hooks';
import { sanitizeHTML } from '@benzinga/frontend-utils';

const tabsData = [
  {
    columnDefs: MemberRecentColumnsDef,
    key: 'recentTrades',
    title: 'Recent Trades',
  },
  {
    columnDefs: MemberLargestColumnsDef,
    key: 'largestTrades',
    title: 'Largest Trades',
  },
  {
    columnDefs: MemberTopColumnsDef,
    key: 'topPerformingPositions',
    title: 'Top Performing',
  },
  {
    columnDefs: MemberTopColumnsDef,
    key: 'activePositions',
    title: 'Active Positions',
  },
];

const americanStates = [
  { name: 'Alaska', state: 'AK' },
  { name: 'Alabama', state: 'AL' },
  { name: 'Arkansas', state: 'AR' },
  { name: 'Arizona', state: 'AZ' },
  { name: 'California', state: 'CA' },
  { name: 'Colorado', state: 'CO' },
  { name: 'Connecticut', state: 'CT' },
  { name: 'Delaware', state: 'DE' },
  { name: 'Florida', state: 'FL' },
  { name: 'Georgia', state: 'GA' },
  { name: 'Hawaii', state: 'HI' },
  { name: 'Iowa', state: 'IA' },
  { name: 'Idaho', state: 'ID' },
  { name: 'Illinois', state: 'IL' },
  { name: 'Indiana', state: 'IN' },
  { name: 'Kansas', state: 'KS' },
  { name: 'Kentucky', state: 'KY' },
  { name: 'Louisiana', state: 'LA' },
  { name: 'Massachusetts', state: 'MA' },
  { name: 'Maryland', state: 'MD' },
  { name: 'Maine', state: 'ME' },
  { name: 'Michigan', state: 'MI' },
  { name: 'Minnesota', state: 'MN' },
  { name: 'Missouri', state: 'MO' },
  { name: 'Mississippi', state: 'MS' },
  { name: 'Montana', state: 'MT' },
  { name: 'North Carolina', state: 'NC' },
  { name: 'Nebraska', state: 'NE' },
  { name: 'New Hampshire', state: 'NH' },
  { name: 'New Jersey', state: 'NJ' },
  { name: 'New Mexico', state: 'NM' },
  { name: 'Nevada', state: 'NV' },
  { name: 'New York', state: 'NY' },
  { name: 'Ohio', state: 'OH' },
  { name: 'Oklahoma', state: 'OK' },
  { name: 'Oregon', state: 'OR' },
  { name: 'Pennsylvania', state: 'PA' },
  { name: 'Rhode Island', state: 'RI' },
  { name: 'South Carolina', state: 'SC' },
  { name: 'South Dakota', state: 'SD' },
  { name: 'Tennessee', state: 'TN' },
  { name: 'Texas', state: 'TX' },
  { name: 'Utah', state: 'UT' },
  { name: 'Virginia', state: 'VA' },
  { name: 'Vermont', state: 'VT' },
  { name: 'Washington', state: 'WA' },
  { name: 'Wisconsin', state: 'WI' },
  { name: 'West Virginia', state: 'WV' },
  { name: 'Wyoming', state: 'WY' },
];

const getMetaProps = slug => {
  const name = slug
    .split('-')
    .map(n => n.charAt(0).toUpperCase() + n.slice(1))
    .join(' ');
  return {
    canonical: 'https://www.benzinga.com/gov-trades/members/' + encodeURIComponent(slug),
    description: `Track all recent stock trades, disclosure filings, and investment performance for US Representative ${name}. Explore detailed trade history, top holdings, and transparency reports—updated in real time.`,
    image:
      'https://cdn.benzinga.com/files/imagecache/bz2_opengraph_meta_image_400x300/sites/all/themes/bz2/images/bz-icon.png',
    pageType: PageType.Tool,
    title: name + ' Stock Trades & Performance - US Congress Trading Disclosures',
  };
};

const SecuritiesDrilldownPage = props => {
  const { metaProps: meta } = props;
  const [activeTab, setActiveTab] = React.useState(0);
  const [tableColumns, setTableColumns] = React.useState(tabsData[0].columnDefs);
  const [tableRows, setTableRows] = React.useState(props?.recentTrades ?? []);
  const [tableData, setTableData] = React.useState({
    activePositions: props?.activePositions ?? [],
    largestTrades: props?.largestTrades ?? [],
    recentTrades: props?.recentTrades ?? [],
    topPerformingPositions: props?.topPerformingPositions ?? [],
  });
  const [congressMembers, setCongressMembers] = React.useState(props?.congressMembers ?? []);
  const [profile, setProfile] = React.useState(props?.profile ?? null);
  const [stats, setStats] = React.useState(props?.stats ?? null);
  const [topPerformingPositions, setTopPerformingPositions] = React.useState(props?.topPerformingPositions ?? []);
  const [performance, setPerformance] = React.useState(props?.performance ?? null);

  const [isLoading, setIsLoading] = React.useState(props?.error || !props?.profile);
  const [pageError, setPageError] = React.useState(false);
  const router = useRouter();
  const session = React.useContext(SessionContext);
  const isLoggedIn = useIsUserLoggedIn();
  const [blurPage, setBlurPage] = React.useState(false);

  const [showFallbackImage, setShowFallbackImage] = React.useState(false);

  const lastPosition = performance?.daily_trading_performance?.[performance.daily_trading_performance?.length - 1];
  const overallPerformance = performance?.portfolio_return?.ttm_overall_return * 100;

  const paramName = Array.isArray(router?.query?.name)
    ? router.query.name[0].replaceAll('-', ' ').replaceAll('.', '')
    : router?.query?.name?.replaceAll('-', ' ').replaceAll('.', '');

  const handleTabClick = (index: number) => {
    const tabData = tabsData[index];
    setActiveTab(index);
    setTableColumns(tabData.columnDefs);
    setTableRows(tableData[tabData.key] ?? []);

    session.getManager(TrackingManager).trackPageEvent('view', {
      page_section: 'gov-trades-members',
      page_tab: tabData.title,
    });
  };

  const handleGoBack = () => {
    if (window.history.length > 1) {
      window.history.back();
    } else {
      router.push('/gov-trades/members');
    }
  };

  React.useEffect(() => {
    const getClientSideProps = async (paramName: string) => {
      const members = await session.getManager(GovTradesManager).getMembers();
      const member = members?.ok?.congressional_members?.find(
        member => member.filer_info?.display_name?.replaceAll('.', '')?.toLowerCase() === paramName,
      );
      if (!member) {
        setPageError(true);
        setIsLoading(false);
        return;
      }
      const memberData = await session.getManager(GovTradesManager).getMember(member?.filer_info?.member_id);

      const performance = await session
        .getManager(GovTradesManager)
        .getTradingPerformanceByMember(member?.filer_info?.member_id);

      let topPerformingPositions = [...(memberData?.ok?.top_performing_positions ?? [])].map(position => {
        position.ticker = position.symbol;
        return position;
      });

      topPerformingPositions = await injectLogos(topPerformingPositions);
      const recentTrades = await injectLogos(memberData?.ok?.recent_trades ?? []);
      const largestTrades = await injectLogos(memberData?.ok?.largest_trades ?? []);
      let activePositions = performance?.ok?.active_positions ?? [];
      if (activePositions.length > 0) {
        activePositions = await injectLogos(
          activePositions.map(p => {
            p.ticker = p.symbol;
            return p;
          }),
        );
      }
      const clientTableData = {
        activePositions: activePositions ?? [],
        largestTrades: largestTrades ?? [],
        recentTrades: recentTrades ?? [],
        topPerformingPositions: topPerformingPositions ?? [],
      };

      setIsLoading(false);
      setPerformance(performance?.ok ?? null);
      setTopPerformingPositions(topPerformingPositions ?? []);
      setCongressMembers(members?.ok?.congressional_members ?? []);
      setProfile(member?.filer_info ?? null);
      setStats(member?.stats ?? null);
      setTableRows(recentTrades ?? []);
      setTableData(clientTableData);
    };

    if (props?.error || paramName !== props?.profile?.display_name?.toLowerCase()) {
      getClientSideProps(paramName as string);
    }
  }, [session, props?.error, router.query.name, props?.profile?.display_name, paramName]);

  React.useEffect(() => {
    const m = getMetaProps(paramName);
    if (meta?.canonical !== m.canonical) {
      session.getManager(TrackingManager).setMeta(meta);
    }
  }, [session, meta, paramName]);

  React.useEffect(() => {
    if (!isLoggedIn) {
      setBlurPage(true);
    }
  }, [isLoggedIn, paramName]);

  if (isLoading) {
    return (
      <div className="w-full h-96">
        {meta && <Meta {...meta} canonical={meta.canonical} />}
        <GovernmentTradesHeader activeTabTitle="Securities" />
        <div className="w-full h-full flex justify-center items-center">
          <div>Loading...</div>
        </div>
      </div>
    );
  }

  if (pageError) {
    return (
      <div className="w-full h-96">
        {meta && <Meta {...meta} canonical={meta.canonical} />}
        <GovernmentTradesHeader activeTabTitle="Members" />
        <div className="w-full h-full flex flex-col justify-center items-center">
          <div>This profile is currently unavailable. Please try again later.</div>
          <button className="px-4 py-2 bg-bzblue-600 text-white rounded-md mt-8" onClick={handleGoBack}>
            Go back
          </button>
        </div>
      </div>
    );
  }

  return (
    <SecuritiesDrilldownPageWrapper>
      {meta && <Meta {...meta} canonical={meta.canonical} />}
      <GovernmentTradesHeader activeTabTitle="Members" />
      <div className={classNames({ 'blur-md pointer-events-none': blurPage, 'main-body gap-4': true })}>
        <div className="mb-4">
          <GovSearchBarV2 members={congressMembers} securities={props?.securitiesList} />
        </div>
        <div className="flex flex-col lg:flex-row items-start gap-8 w-full">
          <div className="">
            {showFallbackImage ? (
              <div className="flex flex-col">
                <IoPersonSharp className="company-logo" />
                {profile?.status === 'Former Member' && (
                  <div className="text-sm pb-4 text-gray-700 text-center mt-1">Status: Former Member</div>
                )}
              </div>
            ) : (
              <img
                alt={`${profile?.display_name} headshot`}
                className="company-logo"
                onError={({ currentTarget }) => {
                  currentTarget.onerror = null; // prevents looping
                  setShowFallbackImage(true);
                }}
                src={profile?.headshot}
              />
            )}
          </div>
          <div>
            <div className="mb-4">
              <h1 className="company-name">{profile?.display_name} - US Congress Member Stock Trading Profile</h1>
              <p>
                View a full summary of {profile?.display_name}’s disclosed trades in real-time, with performance, top
                holdings, and detailed historical activity.
              </p>
              <a className="text-bzblue-700 w-fit" href={profile?.website}>
                {profile?.website?.replace('https', '')?.replace(/[^a-zA-Z.]/g, '')}
              </a>
            </div>
            <div className="flex flex-col lg:flex-row gap-8 items-start">
              <ProfileSummary profile={profile} stats={stats} />
              <div className="card w-full">
                <div className="flex flex-col md:flex-row gap-4">
                  <div>
                    <div className="card-title">{profile?.display_name?.split(' ')[0]}&apos;s Trading Performance</div>
                    <div className="light-text my-4">
                      {lastPosition && typeof overallPerformance === 'number'
                        ? `${profile?.display_name?.split(' ')?.[0]} has performed ${lastPosition?.port > lastPosition?.sp ? 'above' : 'below'} the market over the past 12 months, ${overallPerformance > 0 ? 'gaining' : 'losing'} ${overallPerformance ? overallPerformance.toFixed(2) : 0}% on average.`
                        : `${profile?.display_name?.split(' ')?.[0]} trading performance data over the past 12 months is currently not available. Please try again later.`}
                    </div>
                  </div>
                  <DualLineChart
                    data={performance?.daily_trading_performance}
                    memberLabel={profile?.display_name?.split(' ')?.[0]}
                  />
                </div>
                <div className="divider" />
                <div className="chart-label-wrapper">
                  <ChartKeyLabel color="#4f86f2" label={profile?.display_name?.split(' ')?.[0]} />
                  <ChartKeyLabel color="#f367bb" label="S&P 500" />
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Top trades */}
        {topPerformingPositions.length > 0 && (
          <div className="mt-4">
            <h3 className="uppercase text-base pb-2">top trades</h3>
            <div className="flex gap-4 overflow-y-hidden flex-wrap flex-row h-12">
              {topPerformingPositions.map((trade, index) => (
                <TradeRow key={index} {...trade} percent={trade.return} />
              ))}
            </div>
          </div>
        )}

        {/* Tabs */}
        <div className="tabs-row flex items-center gap-4 wrap mt-4 overflow-x-scroll">
          {tabsData.map((tab, index) => (
            <button
              className={`securities-tab ${activeTab === index ? 'active' : ''}`}
              key={index}
              onClick={() => handleTabClick(index)}
            >
              <div className="flex items-center w-full justify-center">
                <div className={`tab-title ${activeTab === index ? 'active' : ''}`}>{tab.title}</div>
              </div>
            </button>
          ))}
        </div>
        <div className="tab-content">
          <div className="table-wrapper">
            <div className="flex flex-col w-full justify-between items-start">
              <h2 className="table-title">
                {tabsData[activeTab].title} {tabsData[activeTab].title === 'Active Positions' ? 'Held' : ''} by{' '}
                {profile?.display_name}
              </h2>
            </div>
            <GovernmentTable columnsDef={tableColumns} height={500} hideTable={true} rowData={tableRows} />
          </div>
          {/* <a className="show-more">Show More</a> */}
        </div>
      </div>
      <NoFirstRender>
        {!isLoggedIn && (
          <AuthContainer
            authMode="register"
            contentType="gov-trades"
            iterationStyle="edge-hard"
            placement="government-trades"
            preventRedirect={true}
          />
        )}
      </NoFirstRender>
    </SecuritiesDrilldownPageWrapper>
  );
};

const ProfileSummary = ({ profile, stats }) => {
  return (
    <div className="member-profile w-full md:max-w-[20rem]">
      <div>
        <span>Chamber</span>
        <span>{profile?.chamber || '-'}</span>
      </div>
      <div>
        <span>Party</span>
        <span>{congressParties?.find(p => p.party === profile?.party)?.name ?? '-'}</span>
      </div>
      <div>
        <span>State</span>
        <span>{americanStates?.find(state => state.state === profile?.state)?.name ?? '-'}</span>
      </div>
      {profile.district && (
        <div>
          <span>District</span>
          <span>{profile?.district}</span>
        </div>
      )}
      <div>
        <span>Total Trades</span>
        <span>{typeof stats?.number_of_trades === 'number' ? stats?.number_of_trades : '-'}</span>
      </div>
      <div>
        <span>Total Volume of Trades</span>
        <span>
          {typeof stats?.total_volume_of_trades === 'number' ? numberShorthand(stats?.total_volume_of_trades) : '-'}
        </span>
      </div>
      <div>
        <span>Last Recorded Trade Date</span>
        <span>{stats?.date_of_last_recorded_trade || '-'}</span>
      </div>
    </div>
  );
};

const ChartKeyLabel = ({ color, label }: { color: string; label: string }) => (
  <div className="flex items-center gap-4 chart-key-label">
    <div className="box" style={{ background: color }} />
    <div className="label">{label}</div>
  </div>
);

const SecuritiesDrilldownPageWrapper = styled.div`
  font-family: 'Manrope', 'Inter', 'Open Sans', sans-serif;
  background: #e1ebfa;
  .main-body {
    padding: 40px;
    max-width: 1400px;
    margin: 0 auto;
    position: relative;

    @media (max-width: 800px) {
      padding: 1rem;
    }

    .table-title {
      color: #192940;
      /* 24/Bold */
      font-size: 24px;
      font-style: normal;
      font-weight: 700;
      line-height: 32px;
      margin-bottom: 4px;
    }

    .card {
      border-radius: 12px;
      background: #fff;
      border: 1px solid #ceddf2;
      padding: 24px;

      .card-title {
        font-size: 24px;
        font-weight: 700;
      }

      .light-text {
        color: #5b7292;
        font-size: 16px;
        font-weight: 400;
      }

      .divider {
        width: 100%;
        background: #e1ebfa;
        height: 1px;
      }
    }

    .show-more {
      cursor: pointer;
      color: #3f83f8;
      text-transform: uppercase;
      text-align: center;
      font-size: 16px;
      font-weight: 700;
      line-height: 20px;
      padding: 8px 24px;
      display: flex;
      justify-content: center;
      align-items: center;
      width: 100%;
      background: #ffffff;
      border-bottom-right-radius: 12px;
      border-bottom-left-radius: 12px;
      border: 1px solid #e1ebfa;
      margin-top: -1px;
    }

    .chart-label-wrapper {
      display: flex;
      align-items: center;
      padding-top: 16px;
      gap: 16px;

      .chart-key-label {
        display: flex;
        gap: 12px;
        align-items: center;

        .box {
          width: 32px;
          height: 16px;
          border-radius: 4px;
        }
        .label {
          color: #5b7292;
          font-size: 14px;
          font-weight: 400;
        }
      }
    }
  }

  .company-logo {
    height: 100%;
    width: 240px;
    border-radius: 12px;
    object-fit: cover;
    background: #fff;
    border: 1px solid #ceddf2;
  }

  .company-name {
    color: #1c2737;
    font-size: 32px;
    font-style: normal;
    font-weight: 700;
    line-height: 48px;
  }
  .company-symbol {
    color: #878787;
    font-size: 16px;
    font-style: normal;
    font-weight: 700;
    line-height: 24px;
  }

  .table-wrapper {
    padding: 16px 32px;
    background: #f2f8ff;
    border-radius: 12px;
    border: 1px solid #e1ebfa;
  }

  .trade-row {
    .stock-long-name {
      white-space: nowrap;
    }
  }

  .tabs-row {
    overflow-x: auto;
  }

  .securities-tab {
    cursor: pointer;
    max-width: 240px;
    width: 100%;
    background: linear-gradient(180deg, rgba(225, 235, 250, 0) 0%, rgba(225, 235, 250, 0.4) 100%);
    display: flex;
    justify-content: center;
    height: 40px;
    padding: 12px 8px;
    align-items: center;
    gap: 16px;
    flex: 1 0 0;

    border-top-left-radius: 4px;
    border-top-right-radius: 4px;
    border: 1px solid #ceddf2;

    &.active {
      background: #ffffff;
      border-bottom: none;
    }

    .tab-title {
      font-size: 16px;
      white-space: nowrap;
      font-weight: 700;
      color: #5b7292;
      line-height: normal;
      text-transform: uppercase;

      &.active {
        color: #3f83f8;
      }
    }

    .tab__value {
      font-size: 12px;
      color: #828282;
      font-weight: 700;
      line-height: normal;
    }
  }

  .tab-content {
    background: #ffffff;
    border: 1px solid #ceddf2;
    padding: 16px;
    margin: 0;

    margin-top: -1px;

    @media (max-width: 800px) {
      padding: 0px;

      .table-wrapper {
        background: white;
        border: none;
        padding: 16px;

        .benzinga-core-table-container table {
          border: 1px solid #e1ebfa;
          border-radius: 8px;
        }
      }
    }
  }

  .member-profile {
    div {
      display: flex;
      flex-direction: row;
      justify-content: space-between;
      gap: 4px;
      padding: 6px 0px;
      border-bottom: 1px solid #e1ebfa;
      &:last-child {
        border-bottom: none;
      }

      span {
        font-size: 14px;
        line-height: 18px;
        &:first-child {
          font-weight: 400;
          color: rgba(25, 41, 64, 0.8);
        }
        &:nth-child(2) {
          font-weight: 700;
          color: #192940;
        }
      }
    }
  }
`;

export const getServerSideProps = async ({ params }) => {
  try {
    const memberNameParam = params?.name?.replaceAll('-', ' ').replaceAll('.', '');
    const memberName = sanitizeHTML(memberNameParam);
    const session = getGlobalSession();
    const members = await session.getManager(GovTradesManager).getMembers();
    const member = members?.ok?.congressional_members?.find(
      member => member?.filer_info?.display_name?.replaceAll('.', '')?.toLowerCase() === memberName,
    );

    if (!member) {
      return {
        props: {
          error: true,
        },
      };
    }

    const stats = member?.stats;
    const memberData = await session.getManager(GovTradesManager).getMember(member?.filer_info.member_id);

    const performance = await session
      .getManager(GovTradesManager)
      .getTradingPerformanceByMember(member?.filer_info.member_id);

    let topPerformingPositions = [...(memberData?.ok?.top_performing_positions ?? [])].map(position => {
      position.ticker = position.symbol;
      return position;
    });

    topPerformingPositions = await injectLogos(topPerformingPositions);
    const recentTrades = await injectLogos(memberData?.ok?.recent_trades ?? []);
    const largestTrades = await injectLogos(memberData?.ok?.largest_trades ?? []);
    let activePositions = performance?.ok?.active_positions ?? [];
    if (activePositions.length > 0) {
      activePositions = await injectLogos(
        activePositions.map(p => {
          p.ticker = p.symbol;
          return p;
        }),
      );
    }

    const securitiesList = await session.getManager(GovTradesManager).getSecuritiesList();

    return {
      props: {
        activePositions: activePositions ?? [],
        congressMembers: members.ok?.congressional_members ?? [],
        largestTrades: largestTrades ?? [],
        metaProps: getMetaProps(params.name),
        performance: performance.ok ?? null,
        profile: memberData?.ok?.filer_info ?? member?.filer_info ?? null,
        recentTrades: recentTrades ?? [],
        securitiesList: securitiesList?.ok ?? [],
        stats: stats ?? null,
        topPerformingPositions: topPerformingPositions ?? [],
      },
    };
  } catch (err) {
    console.error(err);
    return {
      props: {
        error: true,
        metaProps: getMetaProps(params?.name),
      },
    };
  }
};

export default SecuritiesDrilldownPage;
