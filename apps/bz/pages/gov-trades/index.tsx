import React, { useState } from 'react';
import styled from '@benzinga/themetron';
import { getGlobalSession } from '../api/session';
import { injectLogos } from '@benzinga/calendars';

import { GovTradesManager } from '@benzinga/gov-trades';
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>hart, StackedBarChart } from '../../src/components/GovTrades/Charts';
import {
  GovernmentTable,
  GovernmentTradesHeader,
  GovSearchBarV2,
  TradesColumnsDef,
} from '../../src/components/GovTrades';
import { Meta, MetaProps, PageType } from '@benzinga/seo';
import { usePermission } from '@benzinga/user-context';
import { AiOutlineLock, AiOutlineUnlock } from 'react-icons/ai';
import { AuthContainer } from '@benzinga/auth-ui';
import { NoFirstRender } from '@benzinga/hooks';
import { TrackingManager } from '@benzinga/tracking-manager';
import FAQs from 'apps/bz/src/components/FAQs';

const currencyShortForm = (num: number) => {
  const postfixes = [' thousands', ' millions', ' billions'];
  let r = num / 1000;
  let postfixIndex = -1;
  while (r >= 1) {
    postfixIndex++;
    r = r / 1000;
  }
  const formattedNum = num / Math.pow(1000, postfixIndex + 1);
  const positiveFraction = parseInt((formattedNum % 1).toFixed(2));
  return positiveFraction > 0
    ? formattedNum.toFixed(2)
    : formattedNum.toFixed(0) + (postfixIndex > -1 ? postfixes[postfixIndex] : '');
};

const metaProps: MetaProps = {
  canonical: 'https://www.benzinga.com/gov-trades',
  description:
    'Track stock trades made by US Senators and Representatives in real time. Explore recent transactions, most active lawmakers, and market performance trends—updated daily for transparency',
  image:
    'https://cdn.benzinga.com/files/imagecache/bz2_opengraph_meta_image_400x300/sites/all/themes/bz2/images/bz-icon.png',
  pageType: PageType.Tool,
  title: 'US Congress Stock Trades & Government Trading Tracker - Real-Time Disclosures',
};

const FAQsDataSet = [
  {
    question: 'Why do lawmakers report stock trades?',
    answer: `
        Lawmakers are required to report stock trades under the STOCK Act (Stop Trading on Congressional Knowledge Act), which was passed in 2012. This law was created to increase transparency, prevent insider trading, and ensure that public officials are held accountable when their financial interests intersect with their public responsibilities. By disclosing trades over $1,000, the public can monitor whether members of Congress are potentially benefiting from non-public information gained through their positions.
      `,
  },
  {
    question: 'How is the trade data collected?',
    answer: `
      Members of Congress and certain federal officials must file what are known as Periodic Transaction Reports (PTRs) within 45 days of making a qualifying transaction. These reports are submitted to the Clerk of the House or the Secretary of the Senate, depending on the lawmaker’s chamber. While the reports are made publicly available, they are typically posted in PDF format and can be difficult to search or analyze. As a result, journalists, watchdog groups, and third-party platforms often collect and process this data to make it more accessible and useful to the public.
      `,
  },
  {
    question: 'What are the implications of new trades?',
    answer: `When lawmakers report new trades, it can raise questions about potential conflicts of interest, especially if the timing of the trade aligns with legislative actions, hearings, or policy decisions that could influence a company’s stock value. These disclosures allow the public to track patterns, assess the industries lawmakers are investing in, and identify any possible use of privileged information. While a trade disclosure does not automatically indicate misconduct, it can trigger public scrutiny, media attention, or even ethics investigations if the circumstances appear questionable. Ultimately, these reports serve as a tool to hold public officials accountable and maintain trust in government institutions.`,
  },
];

const GovernmentTradesPage = props => {
  const { congressPerformance, metaProps: meta, monthlyOverview, overview, recentTrades, members, securities } = props;
  const session = getGlobalSession();
  const hasPermission = usePermission('com/read', 'unlimited-calendars');
  const [tableData, setTableData] = React.useState(recentTrades);
  const [recentTradesPage, setRecentTradesPage] = useState(0);
  const [showLoginModal, setShowLoginModal] = useState(false);
  const [showLock, setShowLock] = useState(true);

  const congressTTM = congressPerformance?.portfolio_return?.ttm_overall_return * 100;
  const lastPosition =
    congressPerformance?.daily_trading_performance?.[congressPerformance.daily_trading_performance?.length - 1];

  const totalVolume = overview?.invested?.amount + overview?.withdrawn?.amount;
  const lastMonth = monthlyOverview?.data?.[1];
  const lastMonthVolume = lastMonth?.bought?.amount ?? 0 + lastMonth?.sold?.amount ?? 0;
  const lastMonthChange = (lastMonthVolume / totalVolume) * 100;

  const loadMoreRecent = async () => {
    const nextPage = recentTradesPage + 1;
    try {
      const rows = await session.getManager(GovTradesManager).getTrades(20, nextPage);
      if (rows.ok) {
        const newRows = await injectLogos(
          rows.ok.data.map((trade: any) => ({ ...trade, ticker: trade.security.ticker })),
        );
        setRecentTradesPage(nextPage);
        setTableData([...tableData, ...newRows]);
      }
    } catch (err) {
      console.log(err);
    }
  };

  React.useEffect(() => {
    session?.getManager(TrackingManager).setMeta(meta);
  }, [session, meta]);

  React.useEffect(() => {
    if (hasPermission) {
      setShowLock(false);
    } else {
      setShowLock(true);
    }
  }, [hasPermission]);

  return (
    <GovernmentTradesWrapper>
      {meta && <Meta {...meta} canonical={meta.canonical} />}
      <GovernmentTradesHeader />
      <div className="main-body">
        <div className="bg-white p-8 rounded-md shadow-md mb-8">
          <div>
            <h1 className="mb-1">Congress Stock Trades</h1>
            <p>
              Discover the most up-to-date stock trading disclosures from US Congress. Instantly analyze recent
              purchases and sales, spot high-performing trades, and search by ticker or lawmaker—all from one
              transparent dashboard.
            </p>
          </div>
          <div className="mt-8">
            <h2 className="text-base mb-2">Explore Trading by Stock, Politician, or Congress Chamber</h2>
            <GovSearchBarV2 securities={securities} members={members.congressional_members} />
          </div>
        </div>
        <div className="my-8">
          <h2>Overview of Congress Stock Trading</h2>
          <p className="light-text mb-4">
            Monitor stock trades filed by US lawmakers under the STOCK Act. See how political activity shapes investment
            patterns and detect key market themes.
          </p>
          <div className="grid grid-cols-1 lg:grid-cols-4 gap-4">
            <div className="key-stats">
              <div className="key-stats-title">Total Volume</div>
              <div className="stats">{currencyShortForm(totalVolume)}</div>
              <div className="text-sm text-green-500"> +{lastMonthChange.toFixed(2)}% last month</div>
            </div>
            <div className="key-stats">
              <div className="key-stats-title">Active Traders</div>
              <div className="stats">{members?.congressional_members?.length ?? 0}</div>
              <div className="text-sm text-gray-500">Congress Members</div>
            </div>
            <div className="key-stats">
              <div className="key-stats-title">Avg. Performance</div>
              <div className="stats">
                {congressTTM > 0 ? '+' : '-'}
                {congressTTM.toFixed(2)} %
              </div>
              {/* <div className="text-sm text-gray-500">vs S&P 500: +11.2%</div> */}
            </div>
            <div className="key-stats">
              <div className="key-stats-title">Trades</div>
              <div className="stats">{congressPerformance?.active_positions?.length}</div>
              <div className="text-sm text-gray-500">Active Positions</div>
            </div>
          </div>
        </div>
        <div>
          <h2 className="mb-4">Government Trade Performance Insights</h2>
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-4 mb-4">
            <div className="grid gap-4">
              <div className="card">
                {overview?.invested?.amount && overview?.withdrawn?.amount ? (
                  <div className="flex justify-between">
                    <div>
                      <div className="card-title">Congress Trades Over 12 Months</div>
                      <div className="light-text my-4">
                        {overview?.invested?.amount > overview?.withdrawn?.amount ? (
                          <>
                            Over the past 12 months, congress has invested over $
                            {currencyShortForm(overview?.invested?.amount)} into the market.
                          </>
                        ) : (
                          <>
                            Over the past 12 months, congress has pulled over $
                            {currencyShortForm(overview?.withdrawn?.amount)} out of the market.
                          </>
                        )}
                      </div>
                    </div>
                    <PieChart data={overview} />
                  </div>
                ) : (
                  <div className="flex justify-between">
                    <div>
                      <div className="card-title">Congress Trades Over 12 Months</div>
                      <div className="light-text my-4">
                        Congress trades data over the past 12 months is currently not available. Please try again later.
                      </div>
                    </div>
                  </div>
                )}
                <div className="divider" />
                <div className="chart-label-wrapper">
                  <ChartKeyLabel color="#f85656" label="Congress Stock Sales" />
                  <ChartKeyLabel color="#56d288" label="Congress Stock Buys" />
                </div>
              </div>
              <div className="card">
                <div className="flex flex-col md:flex-row gap-4">
                  <div>
                    <div className="card-title">Congress Trading Performance</div>
                    <div className="light-text my-4">
                      {lastPosition && typeof congressTTM === 'number'
                        ? `Congress has performed ${
                            lastPosition.port > lastPosition.sp ? 'above' : 'below'
                          } the market over the
                        past 12 months, ${congressTTM > 0 ? 'gaining' : 'losing'} ${
                          congressTTM ? congressTTM.toFixed(2) : 0
                        }%
                        on average.`
                        : 'Congress trading performance data over the past 12 months is currently not available. Please try again later.'}
                    </div>
                  </div>
                  <DualLineChart data={congressPerformance?.daily_trading_performance} memberLabel="Congress" />
                </div>
                <div className="divider" />
                <div className="chart-label-wrapper">
                  <ChartKeyLabel color="#4f86f2" label="Congress" />
                  <ChartKeyLabel color="#f367bb" label="S&P 500" />
                </div>
              </div>
            </div>
            <div className="card">
              <div className="">
                <div className="card-title">Congressional Investment</div>
                <div className="divider my-4" />
              </div>
              <div className="pb-10">
                <StackedBarChart data={monthlyOverview} />
              </div>
            </div>
          </div>
        </div>
        <div className="grid grid-cols-1 gap-4">
          <div className="card relative card-table">
            <div className="flex justify-between items-center">
              <div className="card-title">
                <h2>Most Recent Congressional Trades</h2>
                <NoFirstRender>
                  {showLock && (
                    <a
                      className="flex items-center font-bold hover:text-bzblue-700 cursor-pointer text-base md:text-lg"
                      href="https://www.benzinga.com/premium/ideas/benzinga-edge-checkout/?t=be8be9we4gewe1be11&utm_source=government-trades"
                      target="_blank"
                    >
                      <AiOutlineUnlock />
                      Unlock all data with Benzinga Edge
                    </a>
                  )}
                </NoFirstRender>
              </div>
            </div>
            <GovernmentTable
              columnsDef={TradesColumnsDef}
              height={440}
              openLoginModal={() => setShowLoginModal(true)}
              rowData={tableData}
              variant="light"
            />
            <div className={`show-more ${showLock ? 'cursor-not-allowed' : 'cursor-pointer'}`} onClick={loadMoreRecent}>
              <NoFirstRender>{showLock && <AiOutlineLock size={16} />}</NoFirstRender>
              Show More
            </div>
          </div>
        </div>
        <div className="mt-12">
          <h2 className="text-bzblue-900">Frequently Asked Questions about Congressional Trading</h2>
          <FAQs FAQsDataSet={FAQsDataSet} title="Frequently Asked Questions about Congressional Trading" />
        </div>
      </div>
      {showLoginModal && (
        <AuthContainer
          authMode="register"
          contentType="gov-trades"
          iterationStyle="edge-hard"
          placement="government-trades"
          preventRedirect={true}
        />
      )}
    </GovernmentTradesWrapper>
  );
};

const ChartKeyLabel = ({ color, label }: { color: string; label: string }) => (
  <div className="flex items-center gap-4 chart-key-label">
    <div className="box" style={{ background: color }} />
    <div className="label">{label}</div>
  </div>
);

const GovernmentTradesWrapper = styled.div`
  font-family: 'Manrope', 'Inter', 'Open Sans', sans-serif;
  background: #e1ebfa;

  .main-body {
    padding: 40px;
    max-width: 1400px;
    margin: 0 auto;

    @media (max-width: 800px) {
      padding: 1rem;
    }

    .card {
      border-radius: 12px;
      background: #fff;
      border: 1px solid #ceddf2;
      padding: 24px;
      display: flex;
      flex-direction: column;
      justify-content: space-between;

      &.card-table {
        padding-bottom: 42px;
      }

      .card-title {
        font-size: 24px;
        font-weight: 700;
        display: flex;
        align-items: center;
        justify-content: space-between;
        width: 100%;
        flex-wrap: wrap;
      }

      .light-text {
        color: #5b7292;
        font-size: 16px;
        font-weight: 400;
      }

      .divider {
        width: 100%;
        background: #e1ebfa;
        height: 1px;
      }

      .show-more {
        /* cursor: pointer; */
        color: #3f83f8;
        text-transform: uppercase;
        text-align: center;
        font-size: 16px;
        font-weight: 500;
        line-height: 20px;
        padding: 10px;
        display: flex;
        justify-content: center;
        align-items: center;
        width: 100%;
        background: rgba(225, 235, 250, 0.5);
        border-bottom-right-radius: 12px;
        border-bottom-left-radius: 12px;
        border-top: 1px solid #e1ebfa;
        position: absolute;
        bottom: 0;
        left: 0;
      }
    }

    .key-stats {
      background: #fff;
      border: 1px solid #ceddf2;
      border-radius: 12px;
      padding: 16px;
      display: flex;
      flex-direction: column;
      justify-content: flex-start;
      align-items: flex-start;

      .stats {
        font-weight: 800;
        font-size: 20px;
        color: black;
      }

      .key-stats-title {
        font-size: 14px;
        color: #5b7292;
        margin-bottom: 2px;
      }
    }
  }

  .chart-label-wrapper {
    display: flex;
    align-items: center;
    padding-top: 16px;
    gap: 16px;
  }

  .chart-key-label {
    display: flex;
    gap: 12px;
    align-items: center;

    .box {
      width: 32px;
      height: 16px;
      border-radius: 4px;
    }
    .label {
      color: #5b7292;
      font-size: 14px;
      font-weight: 400;
    }
  }
`;

export async function getServerSideProps() {
  try {
    const session = getGlobalSession();
    const trades = await session.getManager(GovTradesManager).getTrades(20, 0);
    const overview = await session.getManager(GovTradesManager).getOverview();
    const monthlyOverview = await session.getManager(GovTradesManager).getOverviewMonthly();
    const congressPerformance = await session.getManager(GovTradesManager).getCongressTradingPerformance();
    const members = await session.getManager(GovTradesManager).getMembers();
    const securities = await session.getManager(GovTradesManager).getSecuritiesList();

    let recentTrades = trades?.ok?.data?.map(trade => {
      return {
        ...trade,
        company_name: trade.security.name,
        ticker: trade.security.ticker,
      };
    });
    recentTrades = recentTrades ? await injectLogos(recentTrades) : [];

    return {
      props: {
        congressPerformance: congressPerformance.ok ?? null,
        metaProps,
        monthlyOverview: monthlyOverview.ok ?? null,
        overview: overview.ok ?? null,
        recentTrades,
        members: members?.ok ?? null,
        securities: securities.ok ?? [],
      },
    };
  } catch (err) {
    console.log(err);
    return {
      props: {
        congressPerformance: null,
        metaProps,
        monthlyOverview: null,
        overview: null,
        recentTrades: [],
      },
    };
  }
}

export default GovernmentTradesPage;
