export const CALENDAR_TOKEN = '97158da3002942879375c27671b91a8f';
export const SPLIT_API_TOKEN = '05cb59286aef4c7eb3406dcb7d7045fb';
export const NEWS_WIIMSONLY_TOKEN = 'b659c28bbbf341149b65aec49124468e';
export const NEWS_TOKEN = 'f69a9049e26b48ce9ffde2085efdb4b8';
export const DATAAPI_TOKEN = '81DC3A5A39D6D1A9D26FA6DF35A34';
export const SEGMENT_DEV_KEY = 'l29fv9xz3v';
export const SEGMENT_PROD_KEY = 'gno9YRq4V6rrQSRSROznvuX0U2RfWHP9';
export const SEGMENT_KEY = process.env.NODE_ENV === 'production' ? SEGMENT_PROD_KEY : SEGMENT_DEV_KEY;
console.log('RUNTIME_ENV: ' + process.env.RUNTIME_ENV, '(RUNTIME_ENV is only available server-side)'); // Only available server-side;
console.log('NODE_ENV: ' + process.env.NODE_ENV);
export const DATAAPI_ROOT = 'https://data-api-next.benzinga.com/rest/';
export const NEWSAPI_ROOT = 'https://api.benzinga.com/api/v2/news';
export const CALENDARSAPI_ROOT = 'https://api.benzinga.com/api/v2/calendar';
export const SEC_CONTENT_TOKEN = '9612e21f0cbf45f5a27993e77c767343';
export const FINAGE_API_KEY = '***************************************';
export const CHARTBEAT_API_KEY = 'cd22f1d0cd5250f0d0cd080685999e69';
export const TRENDING_TICKERS_TOKEN = 'bz.production_2ULO6UL5OXD24HHKRRGO7MMDVG5NQNON';
export const STRIPE_PUBLISHABLE_KEY =
  process.env.NODE_ENV === 'production' ? 'pk_live_jaMpv6EMQhvjzDbiITCMQnjB' : 'pk_test_cKYZbepiuYLumURkRervARLQ';

export const BASE_URL =
  process.env.NODE_ENV === 'development' ? 'http://local.benzinga.com:3000' : process.env.BASE_URL;

export const CHARGEBEE_PUBLISHABLE_KEY =
  process.env.NODE_ENV === 'production'
    ? 'live_s2meQ2VUojWom2IppJ3cfbfrmo9bPnfW'
    : 'test_ujsqcH8bbHZoedgcVtKcoySLsImSDjYl';

export const ITERABLE_API_KEY = '478228650b91482190ef6a9b6956d819'; // server-side key;

export const SESSION_ENV = {
  'benzinga-advanced-news': {
    contentKey: 'TkWiPBzZ5YdSlzmEblWB8eTljd5kXvmb6zNy',
    contentUrl: 'https://content-internal.benzinga.com/content/',
    internalContentUrl: 'https://www.benzinga.com',
    socketUrl: 'wss://api.benzinga.com/api/v3/news/advanced/ws',
    url: 'https://www.benzinga.com/',
  },
  'benzinga-article': {
    key: '2RiuR92vjytxS8r93w3c8WTpGSd3y9Gk',
    url: 'https://www.benzinga.com/',
    wnstnUrl: 'https://editorial-tools.benzinga.com/api/v2/wnstn/',
  },
  'benzinga-authentication': {
    googleClientId: '************-bg460k8jbhbqavvn6ve5ilc1e34s8892.apps.googleusercontent.com',
    url: 'https://accounts.benzinga.com',
  },
  'benzinga-autocomplete': {
    fundsUrl: 'https://www.benzinga.com',
    symbolsKey: '81DC3A5A39D6D1A9D26FA6DF35A34',
    symbolsUrl: 'https://data-api-next.benzinga.com/rest/',
  },
  'benzinga-basic-news': {
    simplyQueryUrl: 'https://www.benzinga.com/',
  },
  'benzinga-calendar': {
    dataUrl: 'https://api.benzinga.com/api/',
    token: '449790be7e324521b2dd3b57aed1ab5d',
    url: 'https://www.benzinga.com/',
  },
  'benzinga-chart': {
    cryptoUrl: 'https://www.benzinga.com/',
    key: '81DC3A5A39D6D1A9D26FA6DF35A34',
    url: 'https://data-api-next.benzinga.com/rest/',
  },
  'benzinga-chat': {
    // streamKey: 'dydh4rhwc8kr',
    streamKey: 'pvxj7fw4u7bv',
    url: 'https://accounts.benzinga.com/',
  },
  'benzinga-content': {
    internalMoneyLayoutUrl: 'http://money-layout.core-prod/lavapress/api/',
    internalMoneyUrl: 'http://benzinga-gen-wordpress.core-prod/lavapress/api/',
    moneyJobsUrl: 'https://money-jobs.benzinga.com/api/',
    // moneyUrl: 'https://www.benzinga.com/money/api/',
    url: 'https://www.benzinga.com/lavapress/api/',
  },
  'benzinga-forex-quotes': {
    url: 'https://www.benzinga.com/',
  },
  'benzinga-internal-news': {
    url: 'https://www.benzinga.com/',
  },
  'benzinga-layout': {
    url: 'https://accounts.benzinga.com',
  },
  'benzinga-licensing': {
    url: 'https://api.benzinga.com/',
  },
  'benzinga-livestream': {
    url: 'https://www.benzinga.com/lavapress/api/',
  },
  'benzinga-movers': {
    url: 'https://www.benzinga.com/api/',
  },
  'benzinga-news': {
    contentKey: 'TkWiPBzZ5YdSlzmEblWB8eTljd5kXvmb6zNy',
    contentUrl: 'https://content-internal.benzinga.com/content/',
    internalContentUrl: 'https://www.benzinga.com',
    socketUrl: 'wss://api.benzinga.com/api/v3/news/advanced/ws',
    url: 'https://www.benzinga.com/',
  },
  'benzinga-notes': {
    url: 'https://accounts.benzinga.com/',
  },
  'benzinga-option-chain': {
    apiKey: '2RiuR92vjytxS8r93w3c8WTpGSd3y9Gk',
    url: 'https://data-api.benzinga.com/',
  },
  'benzinga-permissions': {
    url: 'https://accounts.benzinga.com/',
  },
  'benzinga-quotes': {
    delayedQuoteKey: '81DC3A5A39D6D1A9D26FA6DF35A34',
    delayedQuoteUrl: 'https://data-api-next.benzinga.com/rest/',

    logosKey: '539775a6a04740fc9f7530f102afa105',

    scheduleKey: '81DC3A5A39D6D1A9D26FA6DF35A34',
    scheduleUrl: 'https://data-api-next.benzinga.com/rest/',

    socketUrl: 'wss://pro-quote-v2.benzinga.com/quote/',

    symbolsKey: '54b595f497164e0499409ca93342e394',
    symbolsUrl: 'https://api.benzinga.com/api/',

    tickersKey: '81DC3A5A39D6D1A9D26FA6DF35A34',
    tickersUrl: 'https://data-api-next.benzinga.com/rest/',
  },
  'benzinga-quotes-v3': {
    socketUrl: 'wss://data-api-next.benzinga.com/api/v3/quotes/ws?quote_access=polygon_realtime,finage_crypto',
  },
  'benzinga-scanner': {
    key: 'anBvLgmzgKHJhQdQQzBe24yKFpHwcBJN',
  },
  'benzinga-securities': {
    key: DATAAPI_TOKEN,
    url: 'https://data-api-next.benzinga.com/rest/',
  },
  'benzinga-shop': {
    url: 'https://accounts.benzinga.com/api/v1/',
  },
  'benzinga-signals': {
    restfulUrl: 'https://signals.benzinga.io/signals/api/',
    socketUrl: 'wss://signals.benzinga.io/signals/ws',
  },
  'benzinga-subscriptions': {
    url: 'https://accounts.benzinga.com/',
  },
  'benzinga-trade-ideas': {
    url: 'https://www.benzinga.com/lavapress/api/',
  },
  'benzinga-videos': {
    toolsUrl: 'https://www.benzinga.com/lavapress/api/',
    url: 'https://www.benzinga.com/',
  },
  'benzinga-watchlist': {
    legacyUrl: 'https://www.benzinga.com/',
    url: 'https://api.benzinga.com/',
  },
};

export const SESSION_STAGING_ENV = {
  'benzinga-advanced-news': {
    contentKey: 'TkWiPBzZ5YdSlzmEblWB8eTljd5kXvmb6zNy',
    contentUrl: 'https://content-internal.benzinga.com/content/',
    internalContentUrl: 'https://www.benzinga.com',
    socketUrl: 'wss://api.benzinga.com/api/v3/news/advanced/ws',
    url: 'https://www.benzinga.com/',
  },
  'benzinga-article': {
    analyticsUrl: 'https://bz.zingbot.bz/',
    key: '2RiuR92vjytxS8r93w3c8WTpGSd3y9Gk',
    url: 'https://www.benzinga.com/',
    wnstnUrl: 'https://editorial-tools.zingbot.bz/api/v2/wnstn/',
  },
  'benzinga-authentication': {
    googleClientId: '*************-g29rukffvi2pdjrg9800am4a3591jfi5.apps.googleusercontent.com',
    // url: 'https://accounts.benzinga.com',
    url: 'https://accounts.zingbot.bz',
  },
  'benzinga-autocomplete': {
    fundsUrl: 'https://www.benzinga.com',
    symbolsKey: '81DC3A5A39D6D1A9D26FA6DF35A34',
    symbolsUrl: 'https://data-api-next.benzinga.com/rest/',
  },
  'benzinga-basic-news': {
    simplyQueryUrl: 'https://www.benzinga.com/',
  },
  'benzinga-calendar': {
    dataUrl: 'https://api.benzinga.com/api/',
    token: '449790be7e324521b2dd3b57aed1ab5d',
    url: 'https://www.benzinga.com/',
  },
  'benzinga-chart': {
    ...SESSION_ENV['benzinga-chart'],
  },
  'benzinga-chat': {
    // streamKey: 'dydh4rhwc8kr',
    streamKey: 'pvxj7fw4u7bv',
    url: 'https://accounts.benzinga.com/',
  },
  'benzinga-content': {
    internalMoneyLayoutUrl: 'https://www.benzinga.com/lavapress/api/',
    //internalMoneyUrl: 'http://benzinga-gen-wordpress.core-staging/lavapress/api/',
    internalMoneyUrl: 'https://www.benzinga.com/lavapress/api/',
    moneyJobsUrl: 'https://money-jobs.benzinga.com/api/',
    // moneyUrl: 'https://www.benzinga.com/money/api/',
    url: 'https://www.benzinga.com/lavapress/api/',
  },
  'benzinga-forex-quotes': {
    url: 'https://www.benzinga.com/',
  },
  'benzinga-internal-news': {
    url: 'https://www.benzinga.com/',
  },
  'benzinga-layout': {
    url: 'https://accounts.benzinga.com',
  },
  'benzinga-licensing': {
    url: 'https://api.zingbot.bz/',
  },
  'benzinga-livestream': {
    url: 'https://www.benzinga.com/lavapress/api/',
  },
  'benzinga-notes': {
    url: 'https://accounts.benzinga.com/',
  },
  'benzinga-permissions': {
    url: 'https://accounts.benzinga.com',
  },
  'benzinga-quotes': {
    delayedQuoteKey: '81DC3A5A39D6D1A9D26FA6DF35A34',
    delayedQuoteUrl: 'https://data-api-next.benzinga.com/rest/',

    logosKey: '539775a6a04740fc9f7530f102afa105',

    scheduleKey: '81DC3A5A39D6D1A9D26FA6DF35A34',
    scheduleUrl: 'https://data-api-next.benzinga.com/rest/',

    socketUrl: 'wss://pro-quote-v2.benzinga.com/quote/',

    symbolsKey: '54b595f497164e0499409ca93342e394',
    symbolsUrl: 'https://api.benzinga.com/api/',

    tickersKey: '81DC3A5A39D6D1A9D26FA6DF35A34',
    tickersUrl: 'https://data-api-next.benzinga.com/rest/',
  },
  'benzinga-quotes-v3': {
    socketUrl: 'wss://data-api-next.benzinga.com/api/v3/quotes/ws?quote_access=polygon_realtime,finage_crypto',
  },
  'benzinga-scanner': {
    key: 'anBvLgmzgKHJhQdQQzBe24yKFpHwcBJN',
  },
  'benzinga-securities': {
    key: DATAAPI_TOKEN,
    url: 'https://data-api-next.benzinga.com/rest/',
  },

  'benzinga-shop': {
    url: 'https://accounts.benzinga.com/api/v1/',
  },
  'benzinga-signals': {
    restfulUrl: 'https://signals.benzinga.io/signals/api/',
    socketUrl: 'wss://signals.benzinga.io/signals/ws',
  },
  'benzinga-subscriptions': {
    url: 'https://accounts.benzinga.com/',
  },
  'benzinga-trade-ideas': {
    url: 'https://www.benzinga.com/lavapress/api/',
  },
  'benzinga-videos': {
    toolsUrl: 'https://www.benzinga.com/lavapress/api/',
    url: 'https://www.benzinga.com/',
  },
  'benzinga-watchlist': {
    legacyUrl: 'https://www.benzinga.com/',
    url: 'https://api.benzinga.com/',
  },
};

export const SESSION_DEV_ENV = {
  'benzinga-advanced-news': {
    contentKey: 'TkWiPBzZ5YdSlzmEblWB8eTljd5kXvmb6zNy',
    contentUrl: 'https://content-internal.benzinga.com/content/',
    internalContentUrl: 'http://local.benzinga.com:3000/',
    socketUrl: 'wss://api.benzinga.com/api/v3/news/advanced/ws',
    url: 'http://local.benzinga.com:3000/',
  },
  'benzinga-alts': {
    // url: 'https://money.zingbot.bz/lavapress/api/',
  },
  'benzinga-article': {
    //analyticsUrl: 'http://local.benzinga.com:3000/',
    analyticsUrl: 'https://local.benzinga.com:3000',
    commentsUrl: 'http://local.benzinga.com:3000/',
    externalUrl: 'http://local.benzinga.com:3000',
    //externalUrl: 'http://local.benzinga.com:3000/',
    // internalContentKey: 'tTc3zNxAW9drQpdKrttlHXaT2gKaHPXq',
    // internalContentUrl: 'https://content-internal.zingbot.bz',
    key: '2RiuR92vjytxS8r93w3c8WTpGSd3y9Gk',
    relevantUrl: 'http://local.benzinga.com:3000',
    url: 'https://www.benzinga.com/',
    wnstnUrl: 'https://editorial-tools.benzinga.com/api/wnstn/',
  },
  'benzinga-authentication': {
    googleClientId: '*************-g29rukffvi2pdjrg9800am4a3591jfi5.apps.googleusercontent.com',
    url: 'https://accounts.benzinga.com',
    // url: 'https://accounts.zingbot.bz',
  },
  'benzinga-autocomplete': {
    fundsUrl: 'https://www.benzinga.com',
    symbolsKey: '81DC3A5A39D6D1A9D26FA6DF35A34',
    symbolsUrl: 'https://data-api-next.benzinga.com/rest/',
  },
  'benzinga-basic-news': {
    chartBeatUrl: 'http://local.benzinga.com:3000/',
    simplyQueryUrl: 'http://local.benzinga.com:3000/',
  },
  'benzinga-calendar': {
    dataUrl: 'https://api.benzinga.com/api/',
    token: '449790be7e324521b2dd3b57aed1ab5d',
    url: 'https://www.benzinga.com/',
  },
  'benzinga-chart': {
    ...SESSION_ENV['benzinga-chart'],
    cryptoUrl: 'http://local.benzinga.com:3000/',
  },
  'benzinga-chat': {
    // streamKey: 'dydh4rhwc8kr',
    streamKey: 'pvxj7fw4u7bv',
    url: 'https://accounts.benzinga.com/',
  },
  'benzinga-comments': {
    url: 'http://local.benzinga.com:3000/',
  },
  'benzinga-commodities': {
    url: 'https://www.benzinga.com/lavapress/api/',
    // url: 'https://money.zingbot.bz/lavapress/api/',
  },
  'benzinga-content': {
    internalMoneyLayoutUrl: 'https://www.benzinga.com/lavapress/api/',
    internalMoneyUrl: 'https://www.benzinga.com/lavapress/api/',
    // moneyUrl: 'https://money.zingbot.bz/lavapress/api/',
    // url: 'https://money.zingbot.bz/lavapress/api/',
    moneyJobsUrl: 'https://money-jobs.benzinga.com/api/',
    // moneyUrl: 'https://www.benzinga.com/money/api/',
    url: 'https://www.benzinga.com/lavapress/api/',
    // url: 'https://www.benzinga.com/lavapress/api/',
  },
  'benzinga-forex-quotes': {
    url: 'http://local.benzinga.com:3000/',
  },
  'benzinga-internal-news': {
    url: 'http://local.benzinga.com:3000/',
  },
  'benzinga-layout': {
    url: 'https://accounts.benzinga.com',
  },
  'benzinga-licensing': {
    url: 'https://api.zingbot.bz/',
  },
  'benzinga-livestream': {
    url: 'https://www.benzinga.com/lavapress/api/',
  },
  'benzinga-movers': {
    url: 'http://local.benzinga.com:3000/api/',
  },
  'benzinga-news': {
    contentKey: 'TkWiPBzZ5YdSlzmEblWB8eTljd5kXvmb6zNy',
    contentUrl: 'https://content-internal.benzinga.com/content/',
    internalContentUrl: 'http://local.benzinga.com:3000/',
    socketUrl: 'wss://api.benzinga.com/api/v3/news/advanced/ws',
    url: 'http://local.benzinga.com:3000/',
  },
  'benzinga-notes': {
    url: 'https://accounts.benzinga.com/',
  },
  'benzinga-permissions': {
    url: 'https://accounts.benzinga.com',
  },
  'benzinga-quotes': {
    analysisUrl: 'http://local.benzinga.com:3000/',

    delayedQuoteKey: '81DC3A5A39D6D1A9D26FA6DF35A34',
    delayedQuoteUrl: 'https://data-api-next.benzinga.com/rest/',

    logosKey: '539775a6a04740fc9f7530f102afa105',

    scheduleKey: '81DC3A5A39D6D1A9D26FA6DF35A34',
    scheduleUrl: 'https://data-api-next.benzinga.com/rest/',

    socketUrl: 'wss://pro-quote-v2.benzinga.com/quote/',

    symbolsKey: '54b595f497164e0499409ca93342e394',
    symbolsUrl: 'https://api.benzinga.com/api/',

    tickersKey: '81DC3A5A39D6D1A9D26FA6DF35A34',
    tickersUrl: 'https://data-api-next.benzinga.com/rest/',
  },
  'benzinga-quotes-v3': {
    socketUrl: 'wss://data-api-next.benzinga.com/api/v3/quotes/ws?quote_access=polygon_realtime,finage_crypto',
  },
  'benzinga-scanner': {
    key: 'anBvLgmzgKHJhQdQQzBe24yKFpHwcBJN',
  },
  'benzinga-securities': {
    key: DATAAPI_TOKEN,
    url: 'https://data-api-next.benzinga.com/rest/',
  },
  'benzinga-shop': {
    url: 'https://accounts.benzinga.com/api/v1/',
  },
  'benzinga-signals': {
    restfulUrl: 'https://signals.benzinga.io/signals/api/',
    socketUrl: 'wss://signals.benzinga.io/signals/ws',
  },
  'benzinga-subscriptions': {
    url: 'https://accounts.benzinga.com/',
  },
  'benzinga-trade-ideas': {
    url: 'https://money.zingbot.bz/lavapress/api/',
  },
  'benzinga-videos': {
    toolsUrl: 'https://www.benzinga.com/lavapress/api/',
    url: 'http://local.benzinga.com:3000/',
  },
  'benzinga-watchlist': {
    legacyUrl: 'https://www.benzinga.com/',
    url: 'https://api.benzinga.com/',
  },
};

export const SESSION_SANDBOX_ENV = {
  ...SESSION_STAGING_ENV,
  'benzinga-article': {
    ...SESSION_STAGING_ENV['benzinga-article'],
    url: 'https://www.benzinga.com/',
  },
  'benzinga-authentication': {
    ...SESSION_STAGING_ENV['benzinga-authentication'],
    url: 'https://accounts.benzinga.com',
  },
};

export const SESSION_SERVER_ENV = {
  ...SESSION_ENV,
  'benzinga-advanced-news': {
    ...SESSION_ENV['benzinga-advanced-news'],
    internalContentUrl: 'http://content-server-next.pro-backend',
  },
  'benzinga-calendar': {
    ...SESSION_ENV['benzinga-calendar'],
    //dataUrl: 'https://api.benzinga.com/api/',
    dataUrl: 'http://calendar-api.backend-licensing/api/',
  },
  'benzinga-chart': {
    ...SESSION_ENV['benzinga-chart'],
    url: 'http://chart-api-next.core-prod/rest/',
  },
  'benzinga-movers': {
    ...SESSION_ENV['benzinga-movers'],
    key: '22f84f867c5746fd92ef8e13f5835c02',
    url: 'https://api.benzinga.com/api/v1/market/',
  },
  'benzinga-quotes': {
    ...SESSION_ENV['benzinga-quotes'],
    // delayedQuoteUrl: 'http://quote2-api-delayed.core-prod/rest/',
    delayedQuoteUrl: 'https://data-api-next.benzinga.com/rest/',
    logosUrl: 'http://logo-api-v2.backend-licensing/api/',
    scheduleUrl: 'http://data-api-standard.core-prod/rest/',
    tickersUrl: 'http://data-api-standard.core-prod/rest/',
  },
};

export const SESSION_SERVER_STAGING_ENV = {
  ...SESSION_STAGING_ENV,
  'benzinga-advanced-news': {
    ...SESSION_STAGING_ENV['benzinga-advanced-news'],
    internalContentUrl: 'https://cloudflared-api.benzinga.com',
    // internalContentUrl: 'http://content-server.staging.api.benzinga.dev',
  },
  'benzinga-calendar': {
    ...SESSION_STAGING_ENV['benzinga-calendar'],
    //dataUrl: 'https://api.benzinga.com/api/',
    dataUrl: 'http://calendar-api.backend-licensing/api/',
  },
  'benzinga-chart': {
    ...SESSION_STAGING_ENV['benzinga-chart'],
    url: 'http://chart-api-next.core-staging/rest/',
  },
  'benzinga-quotes': {
    ...SESSION_STAGING_ENV['benzinga-quotes'],
    delayedQuoteUrl: 'http://quote2-api-delayed.core-staging/rest/',
    logosUrl: 'http://api.zingbot.bz/api/',
    scheduleUrl: 'http://data-api-standard.core-staging/rest/',
    tickersUrl: 'http://data-api-standard.core-staging/rest/',
  },
};

export const SESSION_SERVER_SANDBOX_ENV = {
  ...SESSION_STAGING_ENV,
  'benzinga-advanced-news': {
    ...SESSION_STAGING_ENV['benzinga-advanced-news'],
    internalContentUrl: 'https://cloudflared-api.benzinga.com',
    // internalContentUrl: 'http://content-server.staging.api.benzinga.dev',
  },
  'benzinga-article': {
    ...SESSION_STAGING_ENV['benzinga-article'],
    wnstnUrl: 'https://editorial-tools.benzinga.com/api/wnstn/',
  },
  'benzinga-calendar': {
    ...SESSION_STAGING_ENV['benzinga-calendar'],
    //dataUrl: 'https://api.benzinga.com/api/',
  },
  'benzinga-chart': {
    ...SESSION_STAGING_ENV['benzinga-chart'],
  },
  'benzinga-quotes': {
    ...SESSION_STAGING_ENV['benzinga-quotes'],
  },
};

export const SESSION_SERVER_DEV_ENV = {
  ...SESSION_DEV_ENV,
  'benzinga-advanced-news': {
    ...SESSION_ENV['benzinga-advanced-news'],
    internalContentUrl: 'https://cloudflared-api.benzinga.com',
    //internalContentUrl: 'http://localhost:9001',
  },
  'benzinga-movers': {
    ...SESSION_DEV_ENV['benzinga-movers'],
    key: '22f84f867c5746fd92ef8e13f5835c02',
    url: 'https://api.benzinga.com/api/v1/market/',
  },
};
