'use client';

import { useState, useEffect, useMemo, useCallback, useRef } from 'react';
import { AgGridReact } from '@ag-grid-community/react';
import { ColDef, GridReadyEvent, ModuleRegistry, GridApi, RowModelType } from '@ag-grid-community/core';
import { ServerSideRowModelModule } from '@ag-grid-enterprise/server-side-row-model';
import '@ag-grid-community/styles/ag-grid.css';
import '@ag-grid-community/styles/ag-theme-balham.css';
import type { ConferenceCall } from '../types/types';
import { CollectionId } from '../../collections/collectionEntities';
import { fetchConferenceCalls, pollConferenceCalls } from '../../../services/conference';
import styled from 'styled-components';
import '../css/conference-call-list.css';
import { getColumnDefinitions } from '../definitions';
import SearchBar from '../../SearchBar';
import { CollectionGroupId } from '../../collections/collectionEntities';
import { sideBar } from '../../grid/agGridUtils';

// Register the ServerSideRowModel module
ModuleRegistry.registerModules([ServerSideRowModelModule]);

// Simple debounce function implementation
function debounce<T extends (...args: any[]) => any>(func: T, wait: number): (...args: Parameters<T>) => void {
  let timeout: NodeJS.Timeout | null = null;
  return function (...args: Parameters<T>): void {
    if (timeout) {
      clearTimeout(timeout);
    }
    timeout = setTimeout(() => func(...args), wait);
  };
}

// Styled components
const ListContainer = styled.div`
  display: flex;
  flex-direction: column;
  height: 100%;
`;

const HeaderContainer = styled.div`
  border-bottom: 1px solid rgba(16, 22, 26, 0.1);
  flex-shrink: 0;
  background: #fff;
`;

const GridContainer = styled.div`
  flex: 1;
  min-height: 0;
  padding: 2px;
  background: linear-gradient(to bottom, #ffffff, #f5f8fa);
  border-radius: 6px;
  box-shadow: 0 1px 2px rgba(16, 22, 26, 0.1);
`;

const GridContainerDiv = styled.div`
  height: calc(100vh - 200px); /* Dynamic height based on viewport height with space for header */
  width: 100%;
  border: 1px solid #e1e8ed;
  border-radius: 4px;
  box-shadow: 0 1px 3px rgba(16, 22, 26, 0.1);
  overflow: hidden; /* Ensure no overflow outside container */

  /* Make headers fixed */
  .ag-header {
    position: sticky;
    top: 0;
    z-index: 2;
    background-color: #f5f8fa;
  }

  /* Ensure proper overflow behavior */
  .ag-root-wrapper {
    border: 1px solid rgba(16, 22, 26, 0.15);
    border-radius: 3px;
    overflow: hidden;
  }

  .ag-center-cols-viewport {
    overflow-y: auto;
  }

  /* Grid border and header styling */
  .ag-header-row {
    border-bottom: 2px solid #e1e8ed;
  }

  .ag-header-cell {
    border-right: 1px solid #e1e8ed;
    font-weight: 600;
  }

  .ag-cell {
    border-right: 1px solid rgba(16, 22, 26, 0.05);
    border-bottom: 1px solid rgba(16, 22, 26, 0.05);
  }

  /* Zebra striping for rows */
  .ag-row-even {
    background-color: #ffffff;
  }

  .ag-row-odd {
    background-color: #f9fbfd;
  }

  /* Styling for row hover */
  .conference-call-row:hover {
    background-color: #f0f8ff !important;
  }

  /* Selected row styling */
  .ag-row-selected {
    background-color: rgba(16, 107, 163, 0.1) !important;
  }
`;

const CenteredContainer = styled.div`
  display: flex;
  justify-content: center;
  align-items: center;
  height: 300px;
  width: 100%;
`;

const HeaderSection = styled.div`
  display: flex;
  flex-direction: column;
  gap: 8px;
`;

const TitleRow = styled.div`
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 8px;

  h2 {
    font-size: 22px;
    margin: 0;
  }
`;

const SearchAndStatsRow = styled.div`
  display: flex;
  align-items: center;
  gap: 8px;
  width: 100%;
  justify-content: space-between;
`;

const SearchContainer = styled.div`
  background: #f5f8fa;
  border: 1px solid #e1e8ed;
  border-radius: 3px;
  display: flex;
  justify-content: flex-end;
  max-width: 600px;
  padding: 8px;
  width: 100%;
  margin-left: auto;

  .bz-search-bar {
    width: 100%;
  }

  .bp5-control-group {
    box-shadow: 0 0 0 1px rgba(16, 22, 26, 0.1);
    border-radius: 3px;
    overflow: hidden;
  }

  .bp5-input-group {
    flex: 1;
  }
`;

const StatsContainer = styled.div`
  display: flex;
  align-items: center;
  background: #fff;
  border: 1px solid #e1e8ed;
  border-radius: 3px;
  box-shadow: 0 1px 1px rgba(16, 22, 26, 0.1);
  padding: 6px 10px;
  gap: 8px;
  white-space: nowrap;
  margin-right: auto;
`;

const StatusTag = styled.span`
  display: inline-flex;
  align-items: center;
  border-radius: 3px;
  font-size: 12px;
  font-weight: 500;
  padding: 4px 8px;
`;

const LiveTag = styled(StatusTag)`
  background-color: rgba(15, 153, 96, 0.15);
  color: #0d8050;
  cursor: help;
`;

const ManualTag = styled(StatusTag)`
  background-color: rgba(217, 130, 43, 0.15);
  color: #bf7326;
  cursor: pointer;

  &:hover {
    background-color: rgba(217, 130, 43, 0.25);
  }
`;

// Add a styled component for the grid wrapper
const GridWrapper = styled.div`
  background-color: #f5f8fa;
  overflow: auto;
  flex: 1;
  min-height: 0;
`;

interface ConferenceCallListProps {
  selectedCallId?: string;
  onSelectCall: (call: ConferenceCall) => void;
  liveCallId?: string;
}

export function ConferenceCallList({ onSelectCall, selectedCallId }: ConferenceCallListProps) {
  // Core state management - keep only essential states
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [totalCalls, setTotalCalls] = useState(0);
  const [isRequestInProgress, setIsRequestInProgress] = useState(false);

  // Track last date selection time to detect fast vs. slow selections
  const lastDateSelectionRef = useRef<number>(0);
  const dateSelectionTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const debouncedQueryRef = useRef<any>(null);
  const lastDateQueryRef = useRef<string | null>(null);

  // Refs
  const gridApiRef = useRef<GridApi | null>(null);
  const pollingIntervalRef = useRef<(() => void) | null>(null);
  const lastSelectedCallIdRef = useRef<string | null>(null);

  // Default search parameters - includes polling state
  const [queryState, setQueryState] = useState({
    dataType: 'all',
    dateField: 'start_time',
    dateRange: (() => {
      const today = new Date();
      return [today.toISOString().split('T')[0], today.toISOString().split('T')[0]];
    })() as [string, string],
    isPolling: true, // Include polling state in queryState
    pageIndex: 0,
    pageSize: 100,
    query: '',
    queryField: 'ticker',
  });

  // Helper to check if the date range matches today's date
  const isToday = useCallback((dateRange: [string, string]) => {
    const today = new Date().toISOString().split('T')[0];
    return dateRange[0] === today && dateRange[1] === today;
  }, []);

  // Get the last row index for pagination
  const getLastRowIndex = (request: any, results: any[]) => {
    if (!results || results.length === 0) {
      return request.startRow;
    }
    const currentLastRow = request.startRow + results.length;
    return currentLastRow < request.endRow ? currentLastRow : -1;
  };

  // POLLING FUNCTIONS
  // ----------------

  // Stop polling - simplified to just clear the interval
  const stopPolling = useCallback(() => {
    console.log('Stopping polling');
    if (pollingIntervalRef.current) {
      pollingIntervalRef.current();
      pollingIntervalRef.current = null;
    }
    // Update polling state in queryState
    setQueryState(prev => ({
      ...prev,
      isPolling: false,
    }));
  }, []);

  // Convert UTC to ET time
  const convertToET = (dateString: string) => {
    if (!dateString) return '';
    try {
      // Parse the ISO 8601 UTC timestamp
      const date = new Date(dateString);
      if (isNaN(date.getTime())) return ''; // Invalid date

      // Format in ET timezone
      return date.toLocaleTimeString('en-US', {
        hour: '2-digit',
        hour12: false,
        minute: '2-digit',
        second: '2-digit',
        timeZone: 'America/New_York',
      });
    } catch (error) {
      console.error('Error converting time to ET:', error);
      return '';
    }
  };

  // Map data response to grid format
  const mapDataToGridFormat = useCallback((data: any[]) => {
    return data.map(call => {
      const startTimeDate = new Date(call.start_time);
      const date = startTimeDate.toISOString().split('T')[0];
      const startTimeET = convertToET(call.start_time);
      const endTimeET = call.end_time ? convertToET(call.end_time) : '';

      return {
        callTitle: call.call_title,
        companyName: call.name,
        confirmed: call.status === 'COMPLETED' || call.status === 'STARTED',
        createdAt: call.created_at,
        date,
        duration: call.duration,
        endTime: endTimeET,
        exchange: call.exchange,
        headline: call.headline,
        id: call.call_id,
        phoneNumber: '',
        securities: call.securities,
        startTime: startTimeET,
        status: call.status,
        ticker: call.symbol,
        time: startTimeET,
        transcript: { segments: [], speakers: [] },
        transcriptId: call.transcripts?.[0]?.transcript_id || '',
        transcriptType: call.transcripts?.[0]?.type || '',
        updatedAt: call.updated_at,
        webcastUrl: call.webcast_url || call.webcastUrl || '',
      };
    });
  }, []);

  // Declare forward reference using ref
  const getConferenceCallsDataSourceRef = useRef<(forcedDateRange?: [string, string]) => any>(() => null);

  // Start polling - simplified
  const startPolling = useCallback(
    (forcedToday?: boolean) => {
      // First stop any existing polling
      if (pollingIntervalRef.current) {
        pollingIntervalRef.current();
        pollingIntervalRef.current = null;
      }

      // Get fresh today's date
      const today = new Date().toISOString().split('T')[0];

      // Check if we're viewing today - critical check with fresh values
      // Skip this check if forcedToday is true (coming from handleResetPolling)
      if (!forcedToday && (queryState.dateRange[0] !== today || queryState.dateRange[1] !== today)) {
        console.log("Cannot start polling: not viewing today's date");
        return;
      }

      // Update polling flag in query state
      setQueryState(prev => ({
        ...prev,
        isPolling: true,
      }));

      console.log('Starting polling for today:', today);

      // Keep track of the last update time to avoid unnecessary refreshes
      let lastUpdateTime = new Date().getTime();

      // Poll callback function
      const handlePollResponse = (data: any[]) => {
        if (!gridApiRef.current || isRequestInProgress) return;

        // Process only if we have data
        if (data && data.length > 0) {
          // Check if any of the new data has a more recent update time
          const hasNewerData = data.some(call => {
            const updateTime = new Date(call.updated_at).getTime();
            return updateTime > lastUpdateTime;
          });

          if (hasNewerData) {
            console.log('Poll received newer data, refreshing grid');
            lastUpdateTime = new Date().getTime();
            // Only refresh if we have newer data
            gridApiRef.current.refreshServerSide({
              purge: true,
              route: [],
            });
          } else {
            console.log('Poll received data but no newer updates');
          }
        }
      };

      // Start the polling with today's date
      const fromDate = new Date(today);
      const toDate = new Date(today);

      // pollConferenceCalls(callback, dateParams, sortDirection, page, intervalMs)
      pollingIntervalRef.current = pollConferenceCalls(
        handlePollResponse,
        { from: fromDate, to: toDate },
        'desc',
        1,
        20000, // 20 seconds
      );
    },
    [queryState.dateRange, isRequestInProgress],
  );

  // Switch to LIVE mode - simplified
  const handleResetPolling = useCallback(() => {
    console.log('Switching to LIVE mode');

    // Skip if request in progress
    if (isRequestInProgress) return;

    // Always stop existing polling first
    if (pollingIntervalRef.current) {
      pollingIntervalRef.current();
      pollingIntervalRef.current = null;
    }

    // Get fresh today's date
    const today = new Date();
    const todayStr = today.toISOString().split('T')[0];
    const todayDateRange: [string, string] = [todayStr, todayStr];

    // Set loading state
    setLoading(true);

    // Update state with today's date and set polling to true
    setQueryState(prev => ({
      ...prev,
      dateRange: todayDateRange,
      isPolling: true, // Set polling intention
    }));

    // Reset grid and refresh with new data source
    if (gridApiRef.current) {
      gridApiRef.current.paginationGoToFirstPage();

      // Directly fetch data for today
      fetchConferenceCalls({ from: today, to: today }, 'desc', 1)
        .then(response => {
          const { data, pagination } = response;
          setTotalCalls(pagination.hits);
          const mappedData = mapDataToGridFormat(data);

          // Create a data source that will start polling after loading
          const dataSource = {
            getRows: (params: any) => {
              if (params.request.startRow === 0) {
                // Check if we have data to display
                if (mappedData.length === 0) {
                  // Show no rows overlay if no data
                  params.api.showNoRowsOverlay();
                  params.success({
                    rowCount: 0,
                    rowData: [],
                  });
                } else {
                  // Hide overlay if we have data
                  params.api.hideOverlay();
                  params.success({
                    rowCount: pagination.hits > 100 ? -1 : mappedData.length,
                    rowData: mappedData,
                  });
                }

                // Start polling after data is loaded, pass true to force polling
                // regardless of the current queryState
                setTimeout(() => startPolling(true), 200);
              } else {
                // For pagination, use the regular data source
                const regularDataSource = getConferenceCallsDataSourceRef.current(todayDateRange);
                regularDataSource.getRows(params);
              }
            },
          };

          // Set the data source
          gridApiRef.current?.setServerSideDatasource(dataSource);
          setLoading(false);
        })
        .catch(error => {
          console.error('Error fetching initial data:', error);
          setError('Failed to load conference calls. Please try again.');
          setLoading(false);

          // Show error overlay in grid
          if (gridApiRef.current) {
            gridApiRef.current.showNoRowsOverlay();
          }
        });
    }
  }, [isRequestInProgress, mapDataToGridFormat, startPolling, getConferenceCallsDataSourceRef]);

  // Create data source factory
  const createDataSource = useCallback(
    (forcedDateRange?: [string, string]) => {
      // Create a properly typed date range
      let dateRangeToUse: [string, string];

      if (forcedDateRange) {
        dateRangeToUse = [forcedDateRange[0], forcedDateRange[1]];
      } else {
        dateRangeToUse = [queryState.dateRange[0], queryState.dateRange[1]];
      }

      // Check if this is today's date for LIVE mode
      const shouldPoll = isToday(dateRangeToUse);

      return {
        getRows: (params: any) => {
          // Calculate the page number
          const pageNo = Math.floor(params.request.startRow / 100) + 1;

          // Always use the date range from creation time
          const fromDate = new Date(dateRangeToUse[0]);
          const toDate = new Date(dateRangeToUse[1]);

          // Only show loading for first page
          if (params.request.startRow === 0) {
            setLoading(true);
          }

          // Stop polling when paginating
          if (queryState.isPolling && params.request.startRow > 0) {
            stopPolling();
          }

          // Prevent duplicate requests
          if (isRequestInProgress) return;
          setIsRequestInProgress(true);

          // Fetch data with pagination
          fetchConferenceCalls({ from: fromDate, to: toDate }, 'desc', pageNo)
            .then(response => {
              const { data, pagination } = response;
              setTotalCalls(pagination.hits);

              // Map API response to grid format
              const mappedData = mapDataToGridFormat(data);

              // Reset loading state
              setLoading(false);
              setIsRequestInProgress(false);

              // Handle empty results
              if (mappedData.length === 0) {
                params.api.showNoRowsOverlay();
              } else {
                params.api.hideOverlay();
              }

              // Update grid with results
              params.success({
                rowCount: getLastRowIndex(params.request, mappedData),
                rowData: mappedData,
              });

              // Start polling if we're in LIVE mode and this is the first page
              if (shouldPoll && params.request.startRow === 0 && !pollingIntervalRef.current) {
                // Small delay to ensure grid is ready
                setTimeout(startPolling, 100);
              }
            })
            .catch(error => {
              console.error('Error fetching conference calls:', error);
              params.fail();
              setError('Failed to load conference calls. Please try again.');
              setLoading(false);
              setIsRequestInProgress(false);

              // Show error overlay in grid
              if (params.api) {
                params.api.showNoRowsOverlay();
              }
            });
        },
      };
    },
    [queryState, isRequestInProgress, stopPolling, startPolling, mapDataToGridFormat, isToday],
  );

  // Update the ref with the actual implementation
  useEffect(() => {
    getConferenceCallsDataSourceRef.current = createDataSource;
  }, [createDataSource]);

  // Execute the actual query
  const executeQuery = useCallback(
    (
      pageIndex: number,
      pageSize: number,
      dataType: string,
      queryField: string,
      query: string,
      dateField: string,
      dateRange: [string, string],
    ) => {
      // Validate date range
      if (!dateRange || !Array.isArray(dateRange) || dateRange.length !== 2) {
        const today = new Date().toISOString().split('T')[0];
        dateRange = [today, today] as [string, string];
      }

      // Stop any existing polling
      if (pollingIntervalRef.current) {
        pollingIntervalRef.current();
        pollingIntervalRef.current = null;
      }

      // Determine polling status based on date range
      const shouldPoll = isToday(dateRange);

      setLoading(true);

      // Update query state with all parameters
      setQueryState({
        dataType,
        dateField,
        dateRange: [dateRange[0], dateRange[1]] as [string, string],
        isPolling: shouldPoll, // Set polling based on date
        pageIndex,
        pageSize: 100,
        query,
        queryField,
      });

      // Update grid
      if (gridApiRef.current) {
        gridApiRef.current.paginationGoToFirstPage();

        setTimeout(() => {
          if (!isRequestInProgress) {
            const dataSource = getConferenceCallsDataSourceRef.current([dateRange[0], dateRange[1]] as [
              string,
              string,
            ]);
            gridApiRef.current?.setServerSideDatasource(dataSource);
          }
        }, 0);
      }
    },
    [getConferenceCallsDataSourceRef, isRequestInProgress, isToday],
  );

  // Create a debounced executeQuery function
  useEffect(() => {
    debouncedQueryRef.current = debounce(executeQuery, 1500); // 1.5 second debounce

    return () => {
      // Clear any pending timeouts
      if (dateSelectionTimeoutRef.current) {
        clearTimeout(dateSelectionTimeoutRef.current);
      }
    };
  }, [executeQuery]);

  // Handle search bar queries with enhanced debouncing
  const handleQuery = useCallback(
    (
      pageIndex: number,
      pageSize: number,
      dataType: string,
      queryField: string,
      query: string,
      dateField: string,
      dateRange: [string, string],
    ) => {
      console.log(`Query received: dateField=${dateField}, dateRange=[${dateRange[0]}, ${dateRange[1]}]`);

      if (isRequestInProgress) {
        console.log('Request in progress, skipping query');
        return;
      }

      // Check if this is a date change
      const currentDateQuery = JSON.stringify({ dateField, dateRange });
      const isDateChange = currentDateQuery !== lastDateQueryRef.current;
      console.log(
        `Date change detected: ${isDateChange}`,
        `Current: ${currentDateQuery}`,
        `Previous: ${lastDateQueryRef.current}`,
      );

      lastDateQueryRef.current = currentDateQuery;

      // Always use debounce for date changes
      if (isDateChange) {
        console.log('Date change detected, applying aggressive debounce with 1.5s delay');

        // Cancel any pending timeouts
        if (dateSelectionTimeoutRef.current) {
          console.log('Cancelling previous timeout');
          clearTimeout(dateSelectionTimeoutRef.current);
          dateSelectionTimeoutRef.current = null;
        }

        // Use the debounced query function
        if (debouncedQueryRef.current) {
          console.log('Applying debounced query');
          debouncedQueryRef.current(pageIndex, pageSize, dataType, queryField, query, dateField, dateRange);
        }
      } else {
        // For non-date changes, execute immediately
        console.log('Non-date change, executing immediately');
        executeQuery(pageIndex, pageSize, dataType, queryField, query, dateField, dateRange);
      }
    },
    [isRequestInProgress, executeQuery],
  );

  // EVENT HANDLERS
  // ------------

  // Handle cell click to select a call
  const handleCellClicked = useCallback(
    (params: any) => {
      if (!params.data) return;

      const callId = params.data.id;
      // Skip if this call was just selected to prevent duplicate calls
      if (callId === lastSelectedCallIdRef.current || callId === selectedCallId) {
        return;
      }

      // Update refs and call the selection handler
      lastSelectedCallIdRef.current = callId;
      onSelectCall(params.data as ConferenceCall);
    },
    [onSelectCall, selectedCallId],
  );

  // Initialize grid
  const handleGridReady = useCallback(
    (params: GridReadyEvent) => {
      gridApiRef.current = params.api;

      // Setup grid appearance
      params.api.sizeColumnsToFit();
      params.api.setHeaderHeight(40);

      const gridElement = document.querySelector('.ag-root-wrapper');
      if (gridElement) {
        gridElement.classList.add('grid-with-border');
      }

      // Initial data load
      setTimeout(() => {
        const initialDataSource = getConferenceCallsDataSourceRef.current(queryState.dateRange);
        params.api.setServerSideDatasource(initialDataSource);
      }, 0);
    },
    [getConferenceCallsDataSourceRef, queryState.dateRange],
  );

  // LIFECYCLE EFFECTS
  // --------------

  // Cleanup polling on unmount
  useEffect(() => {
    return () => {
      // Clear any pending date selection timeouts
      if (dateSelectionTimeoutRef.current) {
        clearTimeout(dateSelectionTimeoutRef.current);
        dateSelectionTimeoutRef.current = null;
      }

      // Clear polling
      if (pollingIntervalRef.current) {
        pollingIntervalRef.current();
        pollingIntervalRef.current = null;
      }
    };
  }, []);

  // Update the last selected call ref when selectedCallId changes
  useEffect(() => {
    if (selectedCallId) {
      lastSelectedCallIdRef.current = selectedCallId;
    }
  }, [selectedCallId]);

  // GRID OPTIONS
  // ----------

  // Column definitions
  const columnDefs = useMemo(() => {
    const columns = getColumnDefinitions();
    return columns.map(col => ({
      ...col,
      field: col.field,
      filter: true,
      headerName: col.headerName,
      sortable: true,
    }));
  }, []);

  // Default column settings
  const defaultColDef = useMemo(
    () => ({
      flex: 1,
      minWidth: 100,
      resizable: true,
      sortable: true,
    }),
    [],
  );

  // Grid options
  const gridOptions = useMemo(
    () => ({
      cacheBlockSize: 100,
      defaultColDef,
      pagination: true,
      paginationPageSize: 100,
      rowModelType: 'serverSide' as RowModelType,
      rowSelection: 'single' as 'single' | 'multiple',
      serverSideInfiniteScroll: true,
      singleClickEdit: false,
      suppressCellFocus: true,
      suppressLoadingOverlay: true,
      suppressNoRowsOverlay: false,
      suppressScrollOnNewData: true,
    }),
    [defaultColDef],
  );

  // RENDER ERROR STATE
  // ---------------

  if (error) {
    return (
      <ListContainer>
        <HeaderContainer>
          <HeaderSection>
            <TitleRow>
              <h2 className="bp5-heading">Conference Calls</h2>
            </TitleRow>

            <SearchAndStatsRow>
              <StatsContainer>
                <p className="bp5-text-danger" style={{ margin: 0 }}>
                  Error loading conference calls
                </p>
                {isToday(queryState.dateRange) ? (
                  <LiveTag>LIVE Updates</LiveTag>
                ) : (
                  <ManualTag onClick={handleResetPolling}>Manual Mode - Click to Switch to LIVE</ManualTag>
                )}
              </StatsContainer>

              <SearchContainer>
                <SearchBar
                  collectionGroupId={CollectionGroupId.calendar}
                  collectionId={CollectionId.transcript}
                  dataType={queryState.dataType}
                  dateField={queryState.dateField}
                  dateFieldList={[
                    { field: 'start_time', title: 'Start Time' },
                    { field: 'date', title: 'Date' },
                  ]}
                  dateRange={queryState.dateRange}
                  onQuery={handleQuery}
                  pageIndex={queryState.pageIndex}
                  pageSize={queryState.pageSize}
                  query={queryState.query}
                  queryField={queryState.queryField}
                />
              </SearchContainer>
            </SearchAndStatsRow>
          </HeaderSection>
        </HeaderContainer>
        <GridWrapper>
          <CenteredContainer>
            <div className="bp5-non-ideal-state">
              <div className="bp5-non-ideal-state-visual">
                <span className="bp5-icon bp5-icon-error bp5-intent-danger"></span>
              </div>
              <h4 className="bp5-heading">{error}</h4>
              <button className="bp5-button bp5-intent-primary" onClick={handleResetPolling}>
                Retry
              </button>
            </div>
          </CenteredContainer>
        </GridWrapper>
      </ListContainer>
    );
  }

  // RENDER MAIN UI
  // -----------

  return (
    <ListContainer>
      <HeaderContainer>
        <HeaderSection>
          <TitleRow>
            <h2 className="bp5-heading">Conference Calls</h2>
          </TitleRow>

          <SearchAndStatsRow>
            <StatsContainer>
              <span className="bp5-text-muted">
                {loading && queryState.pageIndex === 0
                  ? 'Loading...'
                  : totalCalls > 0
                    ? `${totalCalls} calls found`
                    : 'No calls found'}
              </span>
              {isToday(queryState.dateRange) && queryState.isPolling && (
                <LiveTag title="Live updates - Grid will refresh every 20 seconds to poll the new data from the server">
                  LIVE Updates{' '}
                  <span className="bp5-icon-standard bp5-icon-automatic-updates" style={{ marginLeft: '4px' }} />
                </LiveTag>
              )}
              {(!isToday(queryState.dateRange) || !queryState.isPolling) && (
                <ManualTag
                  onClick={handleResetPolling}
                  title="Manual Updates - Grid won't fetch latest records from the server, please use Live mode for that"
                >
                  Manual Mode - Click to Switch to LIVE
                </ManualTag>
              )}
            </StatsContainer>

            <SearchContainer>
              <SearchBar
                collectionGroupId={CollectionGroupId.calendar}
                collectionId={CollectionId.transcript}
                dataType={queryState.dataType}
                dateField={queryState.dateField}
                dateFieldList={[
                  { field: 'start_time', title: 'Start Time' },
                  { field: 'date', title: 'Date' },
                ]}
                dateRange={queryState.dateRange}
                onQuery={handleQuery}
                pageIndex={queryState.pageIndex}
                pageSize={queryState.pageSize}
                query={queryState.query}
                queryField={queryState.queryField}
              />
            </SearchContainer>
          </SearchAndStatsRow>
        </HeaderSection>
      </HeaderContainer>
      <GridWrapper>
        <GridContainer>
          <GridContainerDiv className="ag-theme-balham">
            <AgGridReact
              columnDefs={columnDefs}
              gridOptions={gridOptions}
              onCellClicked={handleCellClicked}
              onGridReady={handleGridReady}
              overlayNoRowsTemplate="No Results Found"
              rowClass="conference-call-row"
              sideBar={sideBar.showToolPanels(false)}
            />
          </GridContainerDiv>
        </GridContainer>
      </GridWrapper>
    </ListContainer>
  );
}
