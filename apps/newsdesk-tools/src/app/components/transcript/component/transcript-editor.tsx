'use client';

import { useState, useEffect, useRef } from 'react';
import { <PERSON><PERSON>, <PERSON>, Ta<PERSON>, Tab, TextArea, Callout, Dialog, Classes, Icon, InputGroup } from '@blueprintjs/core';
import { IconNames } from '@blueprintjs/icons';
import type { <PERSON><PERSON><PERSON>, Speaker, TranscriptSegment } from '../types/types';
import { SpeakerEditor } from './speaker-editor';
import { TranscriptSegmentEditor } from './transcript-segment-editor';
import { formatDate, formatTime } from '../../../utils/utils';
import { LiveTranscriptView } from './live-transcript-view';
import styled from 'styled-components';
import '../css/transcript-editor.css';
import { uploadConferenceCallAudio } from '../../../services/conference';

// Styled components
const EditorContainer = styled.div`
  display: flex;
  flex-direction: column;
  height: 100%;
  padding: 12px;
  overflow-x: hidden;
`;

const HeaderSection = styled.div`
  background: white;
  border-radius: 6px;
  box-shadow: 0 1px 4px rgba(16, 22, 26, 0.1);
  margin-bottom: 12px;
  padding: 0;
  position: sticky;
  top: 0;
  z-index: 10;
`;

const HeaderTop = styled.div`
  align-items: center;
  background: linear-gradient(to right, #f5f8fa, #ebf1f5);
  border-bottom: 1px solid #d8e1e8;
  border-radius: 6px 6px 0 0;
  display: flex;
  justify-content: space-between;
  padding: 10px 14px;
`;

const HeaderBottom = styled.div`
  border-radius: 0 0 6px 6px;
  padding: 8px 14px;
`;

const CompanyInfo = styled.div`
  align-items: center;
  display: flex;
  margin-bottom: 0;
`;

const CallTitle = styled.h2.attrs({ className: 'bp5-heading' })`
  font-size: 16px;
  font-weight: 600;
  letter-spacing: -0.2px;
  margin: 0;
  margin-right: 8px;
`;

const CallTicker = styled.span`
  background: #2d72d2;
  border-radius: 3px;
  color: white;
  font-size: 11px;
  font-weight: 500;
  padding: 2px 6px;
  text-shadow: 0 1px 0 rgba(0, 0, 0, 0.1);
`;

const CallMeta = styled.div`
  color: #5c7080;
  display: flex;
  align-items: center;
  font-size: 12px;
  margin: 4px 0 0;
  padding: 0;

  & > svg {
    margin-right: 4px;
  }
`;

const CallDescription = styled.div`
  display: flex;
  flex-direction: column;
  gap: 4px;
  margin-top: 2px;
  padding: 0;
`;

const CallDescriptionItem = styled.div`
  border-left: 2px solid #d8e1e8;
  font-size: 12px;
  line-height: 1.4;
  margin-top: 2px;
  padding: 1px 0 1px 8px;
`;

const CallTitleText = styled.div`
  color: #182026;
  font-weight: 500;
`;

const CallHeadlineText = styled.div`
  color: #5c7080;
  font-style: italic;
`;

const ButtonsGroup = styled.div`
  display: flex;
  align-self: center;
  gap: 6px;
  padding: 0;
`;

const SubHeading = styled.h3.attrs({ className: 'bp5-text-muted bp5-heading' })`
  font-size: 15px !important;
  margin-bottom: 4px;
`;

const DetailCell = styled.div``;

const DetailsGrid = styled.div`
  display: grid;
  gap: 24px;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
`;

const TextValue = styled.p``;

const TruncatedText = styled.p`
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
`;

const NotesSection = styled.div`
  margin-top: 24px;
`;

const NotesHeading = styled(SubHeading)`
  margin-bottom: 8px;
`;

const NotesTextArea = styled(TextArea).attrs({ fill: true, growVertically: true })`
  min-height: 100px;
`;

const SummarySection = styled.div`
  display: flex;
  flex-direction: column;
  gap: 24px;
  padding: 16px;
`;

const SummaryCard = styled.div`
  background: #f5f8fa;
  border-left: 4px solid #2d72d2;
  border-radius: 3px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  padding: 18px;
  transition: all 0.2s ease;

  &:hover {
    box-shadow: 0 3px 8px rgba(0, 0, 0, 0.15);
  }
`;

const SummaryTitle = styled.h3.attrs({ className: 'bp5-heading' })`
  color: #2d72d2;
  font-size: 14px;
  margin-bottom: 8px;
  padding: 0;
`;

const SummaryContent = styled.div`
  color: #182026;
  font-size: 13px;
  line-height: 1.4;
  padding: 0;
  white-space: pre-line;
`;

// Add styled components for the editable sections
const EditableContainer = styled.div`
  align-items: center;
  border-radius: 3px;
  display: flex;
  justify-content: space-between;
  padding: 2px 4px;
  position: relative;
  transition: all 0.2s ease;

  &:hover {
    background-color: rgba(167, 182, 194, 0.1);
  }

  .edit-button {
    opacity: 0;
    transition: opacity 0.2s ease;
  }

  &:hover .edit-button {
    opacity: 1;
  }
`;

const EditableContent = styled.div`
  flex: 1;
  padding-right: 6px;
`;

const EditableTitle = styled(CallTitleText)`
  font-weight: 600;
  padding: 1px 0;
`;

const EditableHeadline = styled(CallHeadlineText)`
  color: #5c7080;
  font-style: italic;
  padding: 1px 0;
`;

const EditControls = styled.div`
  display: flex;
  white-space: nowrap;
`;

const EditableCallDescriptionItem = styled(CallDescriptionItem)`
  background: white;
  border-left: 2px solid #2d72d2;
  border-radius: 0 3px 3px 0;
  box-shadow: 0 1px 1px rgba(16, 22, 26, 0.1);
  margin: 4px 0;
  padding: 6px 8px;
  transition: all 0.2s ease;

  &:hover {
    box-shadow: 0 2px 4px rgba(16, 22, 26, 0.15);
  }
`;

const UploadButton = styled(Button)`
  margin-left: 8px;
`;

// Update the tabs container to be scrollable
const TabsContainer = styled.div`
  flex: 1;
  overflow-y: auto;
  overflow-x: hidden;
  border-radius: 6px;
`;

interface TranscriptEditorProps {
  call: ConferenceCall;
  isLive?: boolean;
}

export function TranscriptEditor({ call, isLive = false }: TranscriptEditorProps) {
  const [speakers, setSpeakers] = useState<Speaker[]>(call.transcript.speakers || []);
  const [segments, setSegments] = useState<TranscriptSegment[]>(call.transcript.segments || []);
  const [activeTab, setActiveTab] = useState('transcript');
  const [isFullScreen, setIsFullScreen] = useState(false);
  const [showLiveView, setShowLiveView] = useState(isLive);
  const [showPostProcessingAlert, setShowPostProcessingAlert] = useState(false);
  const [isEditingSummary, setIsEditingSummary] = useState(false);
  const [summaryContent, setSummaryContent] = useState<string>('');
  const [isEditingTitle, setIsEditingTitle] = useState(false);
  const [isEditingHeadline, setIsEditingHeadline] = useState(false);
  const [titleContent, setTitleContent] = useState<string>('');
  const [headlineContent, setHeadlineContent] = useState<string>('');
  const [isUploading, setIsUploading] = useState(false);
  const [uploadMessage, setUploadMessage] = useState<string | null>(null);
  const [uploadMode, setUploadMode] = useState<'file' | 'url'>('file');
  const [fileUrl, setFileUrl] = useState<string>('');
  const [showUploadDialog, setShowUploadDialog] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);

  // Initialize summary content when call changes
  useEffect(() => {
    let summary = '';
    if (typeof call.summary?.summary === 'string') {
      summary = call.summary.summary;
    } else if (call.summary?.summary?.summary) {
      summary = call.summary.summary.summary;
    }
    setSummaryContent(summary);
    setTitleContent(call.callTitle || '');
    setHeadlineContent(call.headline || '');
  }, [call]);

  // Update showLiveView when isLive prop changes
  useEffect(() => {
    console.log('isLive prop changed:', isLive);
    setShowLiveView(isLive);
  }, [isLive]);

  const handleSpeakerUpdate = (updatedSpeaker: Speaker) => {
    setSpeakers(speakers.map(speaker => (speaker.id === updatedSpeaker.id ? updatedSpeaker : speaker)));
  };

  const handleSegmentUpdate = (updatedSegment: TranscriptSegment) => {
    setSegments(segments.map(segment => (segment.id === updatedSegment.id ? updatedSegment : segment)));
  };

  const handleSave = () => {
    console.log('Saving transcript:', { segments, speakers });
  };

  const toggleFullScreen = () => {
    setIsFullScreen(!isFullScreen);
  };

  const handleCallComplete = () => {
    setShowPostProcessingAlert(true);
    setTimeout(() => {
      setShowLiveView(false);
      setShowPostProcessingAlert(false);
    }, 3000);
  };

  const handleSummaryEdit = () => {
    setIsEditingSummary(true);
  };

  const handleSummarySave = () => {
    setIsEditingSummary(false);
    // Here you would typically save the summary to the server
    console.log('Summary saved:', summaryContent);
  };

  const handleSummaryCancel = () => {
    // Reset to original value
    let summary = '';
    if (typeof call.summary?.summary === 'string') {
      summary = call.summary.summary;
    } else if (call.summary?.summary?.summary) {
      summary = call.summary.summary.summary;
    }
    setSummaryContent(summary);
    setIsEditingSummary(false);
  };

  const handleTitleEdit = () => {
    setIsEditingTitle(true);
  };

  const handleTitleSave = () => {
    setIsEditingTitle(false);
    // Here you would typically save the title to the server
    console.log('Title saved:', titleContent);
  };

  const handleTitleCancel = () => {
    setTitleContent(call.callTitle || '');
    setIsEditingTitle(false);
  };

  const handleHeadlineEdit = () => {
    setIsEditingHeadline(true);
  };

  const handleHeadlineSave = () => {
    setIsEditingHeadline(false);
    // Here you would typically save the headline to the server
    console.log('Headline saved:', headlineContent);
  };

  const handleHeadlineCancel = () => {
    setHeadlineContent(call.headline || '');
    setIsEditingHeadline(false);
  };

  const handleUploadClick = () => {
    setShowUploadDialog(true);
  };

  const handleDialogClose = () => {
    setShowUploadDialog(false);
    setFileUrl('');
  };

  const handleModeChange = (mode: 'file' | 'url') => {
    setUploadMode(mode);
    setFileUrl('');
  };

  const handleFileChange = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];

    if (!file) return;

    if (!file.name.endsWith('.mp3') && !file.name.endsWith('.mp4')) {
      setUploadMessage('Please upload an MP3 or MP4 file');
      // Clear the file input
      if (fileInputRef.current) {
        fileInputRef.current.value = '';
      }
      return;
    }

    // Check file size (150 MB max)
    const maxSizeInBytes = 150 * 1024 * 1024; // 150 MB
    if (file.size > maxSizeInBytes) {
      setUploadMessage(
        `File size exceeds the maximum limit of 150 MB. Current size: ${(file.size / (1024 * 1024)).toFixed(2)} MB`,
      );
      // Clear the file input
      if (fileInputRef.current) {
        fileInputRef.current.value = '';
      }
      return;
    }

    await uploadAudio(file);
  };

  const handleUrlSubmit = async () => {
    if (!fileUrl.trim()) {
      setUploadMessage('Please enter a valid file URL');
      return;
    }

    // Basic URL validation
    try {
      new URL(fileUrl);
    } catch {
      setUploadMessage('Please enter a valid URL');
      return;
    }

    await uploadAudio(null, fileUrl);
  };

  const uploadAudio = async (file?: File | null, fileUrl?: string) => {
    setIsUploading(true);
    setUploadMessage(null);
    setShowUploadDialog(false);

    try {
      // Format created_time to match API (YYYY-MM-DDTHH:MM:SS, UTC)
      const dateObj = new Date(call.date + 'T' + call.time);
      const year = dateObj.getUTCFullYear();
      const month = String(dateObj.getUTCMonth() + 1).padStart(2, '0');
      const day = String(dateObj.getUTCDate()).padStart(2, '0');
      const hours = String(dateObj.getUTCHours()).padStart(2, '0');
      const minutes = String(dateObj.getUTCMinutes()).padStart(2, '0');
      const seconds = String(dateObj.getUTCSeconds()).padStart(2, '0');
      const createdTimeFormatted = `${year}-${month}-${day}T${hours}:${minutes}:${seconds}Z`;

      const uploadParams = {
        call_id: call.id,
        call_title: call.callTitle || 'Untitled Call',
        created_time: createdTimeFormatted,
        exchange: call.exchange as 'NASDAQ' | 'NYSE' | 'ASX' | 'TSX' | 'LSE' | 'ALL',
        file: file || undefined,
        file_url: fileUrl || undefined,
        symbol: call.ticker,
      };

      const result = await uploadConferenceCallAudio(uploadParams);

      setUploadMessage(result.message);
    } catch (error: any) {
      setUploadMessage('Upload failed. Please try again.');
    } finally {
      setIsUploading(false);
      // Clear the file input and URL after each attempt
      if (fileInputRef.current) {
        fileInputRef.current.value = '';
      }
      setFileUrl('');
    }
  };

  const hasTranscriptData = segments.length > 0 && speakers.length > 0;

  const editorContent = showLiveView ? (
    <LiveTranscriptView call={call} onCallComplete={handleCallComplete} />
  ) : (
    <div className="transcript-editor-container">
      <Tabs
        className="bp5-tabs-large transcript-tabs"
        id="transcript-tabs"
        onChange={newTabId => setActiveTab(newTabId as string)}
        selectedTabId={activeTab}
      >
        <Tab
          id="transcript"
          panel={
            <div className="tab-panel-content">
              {hasTranscriptData ? (
                <div className="transcript-segments">
                  {segments.map(segment => (
                    <TranscriptSegmentEditor
                      key={segment.id}
                      onUpdate={handleSegmentUpdate}
                      segment={segment}
                      speakers={speakers}
                    />
                  ))}
                </div>
              ) : (
                <div className="empty-state">
                  <div className="bp5-text-muted">
                    <Icon className="empty-state-icon" icon={IconNames.TIME} size={64} />
                    <h3 className="bp5-heading">No Transcript Available</h3>
                    <p>This call doesn't have a transcript yet or is scheduled for the future.</p>
                  </div>
                  <Button text="Create New Transcript" />
                </div>
              )}
            </div>
          }
          title="Transcript"
        />
        <Tab
          id="speakers"
          panel={
            <div className="tab-panel-content">
              <div className="speakers-panel">
                <h3 className="bp5-heading">Edit Speakers</h3>
                {speakers.length > 0 ? (
                  <div className="speakers-list">
                    {speakers.map(speaker => (
                      <SpeakerEditor key={speaker.id} onUpdate={handleSpeakerUpdate} speaker={speaker} />
                    ))}
                  </div>
                ) : (
                  <div className="empty-speakers">
                    <p className="bp5-text-muted">No speakers have been added to this transcript yet.</p>
                    <Button className="add-speaker-btn" text="Add Speaker" />
                  </div>
                )}
              </div>
            </div>
          }
          title="Speakers"
        />
        <Tab
          id="summary"
          panel={
            <div className="tab-panel-content">
              <SummarySection>
                <SummaryCard>
                  <div style={{ alignItems: 'center', display: 'flex', justifyContent: 'space-between' }}>
                    <SummaryTitle>Call Summary</SummaryTitle>
                    <div>
                      {isEditingSummary ? (
                        <>
                          <Button
                            icon={IconNames.TICK}
                            intent="success"
                            minimal={true}
                            onClick={handleSummarySave}
                            small={true}
                            style={{ marginRight: '4px' }}
                          />
                          <Button
                            icon={IconNames.CROSS}
                            intent="danger"
                            minimal={true}
                            onClick={handleSummaryCancel}
                            small={true}
                          />
                        </>
                      ) : (
                        <Button icon={IconNames.EDIT} minimal={true} onClick={handleSummaryEdit} small={true} />
                      )}
                    </div>
                  </div>
                  {isEditingSummary ? (
                    <TextArea
                      fill={true}
                      growVertically={true}
                      onChange={e => setSummaryContent(e.target.value)}
                      style={{
                        fontSize: '14px',
                        lineHeight: '1.5',
                        marginTop: '10px',
                        minHeight: '200px',
                      }}
                      value={summaryContent}
                    />
                  ) : (
                    <SummaryContent>{summaryContent || 'No summary available'}</SummaryContent>
                  )}
                </SummaryCard>
              </SummarySection>
            </div>
          }
          title="Summary"
        />
      </Tabs>
    </div>
  );

  return (
    <>
      <EditorContainer>
        <HeaderSection>
          <HeaderTop>
            <CompanyInfo>
              <CallTitle>{call.companyName}</CallTitle>
              <CallTicker>{call.ticker}</CallTicker>
            </CompanyInfo>

            <ButtonsGroup>
              {!showLiveView && (
                <>
                  <UploadButton
                    icon={IconNames.UPLOAD}
                    loading={isUploading}
                    minimal={true}
                    onClick={handleUploadClick}
                    small={true}
                    text="Upload Audio file"
                  />

                  <Button
                    icon={IconNames.MAXIMIZE}
                    minimal={true}
                    onClick={toggleFullScreen}
                    small={true}
                    title="Expand to full screen"
                  />
                </>
              )}
            </ButtonsGroup>
          </HeaderTop>
          {uploadMessage && (
            <Callout intent={uploadMessage.includes('started') ? 'success' : 'danger'} style={{ marginTop: '8px' }}>
              {uploadMessage}
            </Callout>
          )}
          <HeaderBottom>
            <CallMeta>
              <Icon icon={IconNames.CALENDAR} size={12} />
              {formatDate(call.date)} at {formatTime(call.time)}
            </CallMeta>

            {(titleContent || headlineContent || call.callTitle || call.headline) && (
              <CallDescription>
                {(titleContent || call.callTitle) && (
                  <EditableCallDescriptionItem>
                    {isEditingTitle ? (
                      <div style={{ padding: '2px 0' }}>
                        <InputGroup
                          fill={true}
                          onChange={e => setTitleContent(e.target.value)}
                          placeholder="Enter call title"
                          value={titleContent}
                        />
                        <EditControls style={{ justifyContent: 'flex-end', marginTop: '4px' }}>
                          <Button
                            icon={IconNames.TICK}
                            intent="success"
                            minimal={true}
                            onClick={handleTitleSave}
                            small={true}
                            style={{ marginRight: '2px' }}
                          />
                          <Button
                            icon={IconNames.CROSS}
                            intent="danger"
                            minimal={true}
                            onClick={handleTitleCancel}
                            small={true}
                          />
                        </EditControls>
                      </div>
                    ) : (
                      <EditableContainer>
                        <EditableContent>
                          <EditableTitle>{titleContent || call.callTitle}</EditableTitle>
                          <div style={{ color: '#738694', fontSize: '10px', marginTop: '1px' }}>Title</div>
                        </EditableContent>
                        <Button
                          className="edit-button"
                          icon={IconNames.EDIT}
                          minimal={true}
                          onClick={handleTitleEdit}
                          small={true}
                        />
                      </EditableContainer>
                    )}
                  </EditableCallDescriptionItem>
                )}

                {(headlineContent || call.headline) && (
                  <EditableCallDescriptionItem>
                    {isEditingHeadline ? (
                      <div style={{ padding: '2px 0' }}>
                        <InputGroup
                          fill={true}
                          onChange={e => setHeadlineContent(e.target.value)}
                          placeholder="Enter call headline"
                          value={headlineContent}
                        />
                        <EditControls style={{ justifyContent: 'flex-end', marginTop: '4px' }}>
                          <Button
                            icon={IconNames.TICK}
                            intent="success"
                            minimal={true}
                            onClick={handleHeadlineSave}
                            small={true}
                            style={{ marginRight: '2px' }}
                          />
                          <Button
                            icon={IconNames.CROSS}
                            intent="danger"
                            minimal={true}
                            onClick={handleHeadlineCancel}
                            small={true}
                          />
                        </EditControls>
                      </div>
                    ) : (
                      <EditableContainer>
                        <EditableContent>
                          <EditableHeadline>{headlineContent || call.headline}</EditableHeadline>
                          <div style={{ color: '#738694', fontSize: '10px', marginTop: '1px' }}>Headline</div>
                        </EditableContent>
                        <Button
                          className="edit-button"
                          icon={IconNames.EDIT}
                          minimal={true}
                          onClick={handleHeadlineEdit}
                          small={true}
                        />
                      </EditableContainer>
                    )}
                  </EditableCallDescriptionItem>
                )}
              </CallDescription>
            )}
          </HeaderBottom>
        </HeaderSection>

        {showPostProcessingAlert && (
          <Callout
            icon={IconNames.WARNING_SIGN}
            intent="warning"
            style={{ marginBottom: '16px' }}
            title="Post-processing transcript"
          >
            The call has ended. We're processing the transcript to identify speakers and format the content...
          </Callout>
        )}

        <TabsContainer>
          <Card style={{ borderRadius: '6px', height: '100%', padding: 0 }}>{editorContent}</Card>
        </TabsContainer>
      </EditorContainer>

      {/* Upload Dialog */}
      <Dialog
        isOpen={showUploadDialog}
        onClose={handleDialogClose}
        style={{ width: '500px' }}
        title="Upload Audio File"
      >
        <div className={Classes.DIALOG_BODY}>
          <div style={{ marginBottom: '16px' }}>
            <h4 style={{ marginBottom: '8px' }}>Choose Upload Method</h4>
            <div style={{ display: 'flex', gap: '8px', marginBottom: '16px' }}>
              <Button
                active={uploadMode === 'file'}
                minimal={true}
                onClick={() => handleModeChange('file')}
                text="Upload File"
              />
              <Button
                active={uploadMode === 'url'}
                minimal={true}
                onClick={() => handleModeChange('url')}
                text="Paste URL"
              />
            </div>
          </div>

          {uploadMode === 'file' ? (
            <div>
              <p style={{ color: '#5c7080', fontSize: '14px', marginBottom: '12px' }}>
                Select an MP3 or MP4 file to upload (max 150 MB)
              </p>
              <input
                accept=".mp3,.mp4"
                onChange={handleFileChange}
                ref={fileInputRef}
                style={{ display: 'none' }}
                type="file"
              />
              <Button
                fill={true}
                icon={IconNames.UPLOAD}
                onClick={() => fileInputRef.current?.click()}
                text="Choose File"
              />
            </div>
          ) : (
            <div>
              <p style={{ color: '#5c7080', fontSize: '14px', marginBottom: '12px' }}>
                Paste the URL of your audio file (MP3 or MP4)
              </p>
              <InputGroup
                fill={true}
                onChange={e => setFileUrl(e.target.value)}
                placeholder="https://example.com/audio.mp3"
                value={fileUrl}
              />
            </div>
          )}
        </div>
        <div className={Classes.DIALOG_FOOTER}>
          <div className={Classes.DIALOG_FOOTER_ACTIONS}>
            <Button onClick={handleDialogClose} text="Cancel" />
            {uploadMode === 'url' && (
              <Button
                disabled={!fileUrl.trim()}
                intent="primary"
                loading={isUploading}
                onClick={handleUrlSubmit}
                text="Submit URL"
              />
            )}
          </div>
        </div>
      </Dialog>

      <Dialog
        className="transcript-fullscreen-dialog"
        isOpen={isFullScreen}
        onClose={() => setIsFullScreen(false)}
        style={{ borderRadius: '4px', height: '95vh', width: '95vw' }}
        title={
          <>
            <div style={{ padding: '2px 0' }}>
              {call.companyName} <CallTicker style={{ marginLeft: '6px' }}>{call.ticker}</CallTicker>
            </div>
            {call.callTitle && <div className="dialog-subtitle">{call.callTitle}</div>}
          </>
        }
      >
        <div
          className={Classes.DIALOG_BODY}
          style={{ borderRadius: '0 0 4px 4px', height: 'calc(95vh - 120px)', overflow: 'hidden', padding: 0 }}
        >
          {editorContent}
        </div>
        <div className={Classes.DIALOG_FOOTER} style={{ padding: '10px 20px' }}>
          <div className={Classes.DIALOG_FOOTER_ACTIONS} style={{ gap: '10px' }}>
            <Button onClick={() => setIsFullScreen(false)} text="Close" />
            <Button
              disabled={!hasTranscriptData}
              icon={IconNames.FLOPPY_DISK}
              intent="primary"
              onClick={handleSave}
              text="Save Changes"
            />
          </div>
        </div>
      </Dialog>
    </>
  );
}
