import React from 'react';
import { Icon, IconName, Intent, Tooltip } from '@blueprintjs/core';
import { IconNames } from '@blueprintjs/icons';

// Custom cell renderer for status column - icon only
export const StatusCellRenderer = (params: any) => {
  const status = params.value;
  let icon: IconName = IconNames.HELP;
  let intent: Intent = Intent.NONE;
  let tooltip = status;

  switch (status?.toUpperCase()) {
    case 'COMPLETED':
      icon = IconNames.TICK_CIRCLE;
      intent = Intent.SUCCESS;
      tooltip = 'Call completed successfully';
      break;
    case 'STARTED':
      icon = IconNames.PLAY;
      intent = Intent.PRIMARY;
      tooltip = 'Call in progress';
      break;
    case 'IN_PROGRESS':
      icon = IconNames.PLAY;
      intent = Intent.PRIMARY;
      tooltip = 'Call in progress';
      break;
    case 'SCHEDULED':
      icon = IconNames.CALENDAR;
      intent = Intent.WARNING;
      tooltip = 'Call scheduled for the future';
      break;
    case 'CANCELED':
      icon = IconNames.CROSS_CIRCLE;
      intent = Intent.DANGER;
      tooltip = 'Call was canceled';
      break;
    case 'FAILED':
      icon = IconNames.ERROR;
      intent = Intent.DANGER;
      tooltip = 'Call failed to complete';
      break;
    case 'PENDING':
      icon = IconNames.TIME;
      intent = Intent.WARNING;
      tooltip = 'Call is pending';
      break;
    case 'LINK_EXTRACTION_FAILED':
      icon = IconNames.LINK;
      intent = Intent.DANGER;
      tooltip = 'Link extraction failed';
      break;
    default:
      icon = IconNames.HELP;
      intent = Intent.NONE;
      tooltip = status || 'Unknown status';
  }

  // Render only the icon with a tooltip
  return (
    <div style={{ display: 'flex', justifyContent: 'center' }}>
      <Tooltip content={tooltip}>
        <Icon icon={icon} intent={intent} size={16} />
      </Tooltip>
    </div>
  );
};

// Custom cell renderer for transcript type column
export const TranscriptTypeCellRenderer = (params: any) => {
  const type = params.value;

  if (!type) return null;

  let icon: IconName = IconNames.DOCUMENT;
  let intent: Intent = Intent.NONE;
  let tooltip = type;

  switch (type.toUpperCase()) {
    case 'LIVE':
      icon = IconNames.PULSE;
      intent = Intent.SUCCESS;
      tooltip = 'Live Transcript';
      break;
    case 'NON_LIVE':
      icon = IconNames.DOCUMENT_SHARE;
      intent = Intent.PRIMARY;
      tooltip = 'Non-Live Transcript';
      break;
    default:
      icon = IconNames.DOCUMENT;
      intent = Intent.NONE;
      tooltip = type;
  }

  // Render only the icon with a tooltip, centered in the cell
  return (
    <div style={{ display: 'flex', justifyContent: 'center' }}>
      <Tooltip content={tooltip}>
        <Icon icon={icon} intent={intent} size={16} />
      </Tooltip>
    </div>
  );
};

// Custom cell renderer for call link column
export const CallLinkCellRenderer = (params: any) => {
  const webcastUrl = params.value;

  if (!webcastUrl) {
    return (
      <div style={{ display: 'flex', justifyContent: 'center' }}>
        <Tooltip content="No webcast link available">
          <Icon icon={IconNames.LINK} intent={Intent.NONE} size={16} style={{ opacity: 0.3 }} />
        </Tooltip>
      </div>
    );
  }

  const handleClick = (e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();
    window.open(webcastUrl, '_blank', 'noopener,noreferrer');
  };

  return (
    <div style={{ display: 'flex', justifyContent: 'center' }}>
      <Tooltip content="Click to open webcast link">
        <Icon
          icon={IconNames.LINK}
          intent={Intent.PRIMARY}
          onClick={handleClick}
          size={16}
          style={{ cursor: 'pointer' }}
        />
      </Tooltip>
    </div>
  );
};
