import axios from 'axios';

/**
 * Formats date object to API expected format: YYYY-MM-DDTHH:MM:SS
 * @param date The date to format
 * @param isEndOfDay Set to true to format with end of day time (23:59:59)
 * @returns Formatted date string
 */
const formatDateForApi = (date: Date, isEndOfDay = false): string => {
  // Make sure the date is valid
  if (!(date instanceof Date) || isNaN(date.getTime())) {
    console.warn('Invalid date provided:', date);
    // Return today as fallback
    const today = new Date();
    return formatDateForApi(today, isEndOfDay);
  }

  const year = date.getUTCFullYear();
  const month = String(date.getUTCMonth() + 1).padStart(2, '0');
  const day = String(date.getUTCDate()).padStart(2, '0');

  const timeString = isEndOfDay ? '23:59:59' : '00:00:00';

  return `${year}-${month}-${day}T${timeString}`;
};

/**
 * Fetches conference call data from the API
 * This function retrieves conference call data from the Benzinga API
 * @param dateParams Optional parameters to override default date range
 * @param sortDirection Optional sort direction, defaults to 'desc'
 * @param page Optional page number, defaults to 1
 * @param pageSize Optional page size, defaults to 100
 * @returns Promise that resolves to the call data and pagination info
 */
export async function fetchConferenceCalls(
  dateParams?: { from?: Date; to?: Date },
  sortDirection: 'asc' | 'desc' = 'desc',
  page = 1,
  pageSize = 100,
) {
  try {
    // Get current date for default date range
    const today = new Date();

    // Create default dates if not provided
    let fromDate: Date;
    let toDate: Date;

    if (dateParams?.from && dateParams?.to) {
      // Use provided dates
      fromDate = new Date(dateParams.from);
      toDate = new Date(dateParams.to);

      // Log to verify correct dates are being used
      console.log('Using provided date range:', { fromDate, toDate });
    } else {
      // Use defaults - today
      fromDate = new Date(today);
      toDate = new Date(today);
      console.log('Using default date range (today):', { today });
    }

    // Format dates for API
    const fromFormatted = formatDateForApi(fromDate);
    const toFormatted = formatDateForApi(toDate, true); // end of day for 'to' date

    console.log('From/To Date formatted:', fromFormatted, toFormatted);

    const response = await axios.get(
      `${global.env.TRANSCRIPT_V1_ENDPOINT}/calls?token=${global.env.TRANSCRIPT_V1_TOKEN}&page_size=${pageSize}&page=${page}&from=${fromFormatted}&to=${toFormatted}&sort_by=${sortDirection}`,
    );

    // Check if response has data property and pagination info
    if (response.data) {
      return {
        data: response.data.data || [],
        pagination: response.data.pagination || {
          hits: 0,
          page: page,
          page_count: 1,
          page_size: pageSize,
        },
      };
    }

    // Default to empty array if no data
    return {
      data: [],
      pagination: {
        hits: 0,
        page: page,
        page_count: 1,
        page_size: pageSize,
      },
    };
  } catch (error) {
    console.error('Error fetching conference calls:', error);
    throw error;
  }
}

/**
 * Sets up polling for conference calls at regular intervals
 * @param callback Function to call with the fetched conference call data
 * @param dateParams Optional parameters to override default date range
 * @param sortDirection Optional sort direction, defaults to 'desc'
 * @param page Page number for pagination, defaults to 1
 * @param intervalMs Polling interval in milliseconds (default: 20000 - 20 seconds)
 * @returns Function to stop the polling
 */
export function pollConferenceCalls(
  callback: (data: any[]) => void,
  dateParams?: { from?: Date; to?: Date },
  sortDirection: 'asc' | 'desc' = 'desc',
  page = 1,
  intervalMs = 20000,
) {
  // Fetch immediately
  fetchConferenceCalls(dateParams, sortDirection, page)
    .then(response => callback(response.data))
    .catch(error => console.error('Error in initial conference calls fetch:', error));

  // Set up interval
  const intervalId = setInterval(async () => {
    try {
      const response = await fetchConferenceCalls(dateParams, sortDirection, page);
      callback(response.data);
    } catch (error) {
      console.error('Error in conference calls polling:', error);
    }
  }, intervalMs);

  // Return function to stop polling
  return () => clearInterval(intervalId);
}

/**
 * Fetches detailed information for a specific conference call
 * @param callId The ID of the call to fetch details for
 * @returns Promise that resolves to the call details
 */
export async function fetchCallDetails(callId: string) {
  try {
    const response = await axios.get(
      `${global.env.TRANSCRIPT_V1_ENDPOINT}/calls/${callId}?token=${global.env.TRANSCRIPT_V1_TOKEN}&clean=true`,
    );
    return response.data;
  } catch (error) {
    console.error(`Error fetching details for call ${callId}:`, error);
    throw error;
  }
}

interface UploadConferenceCallAudioParams {
  call_id?: string;
  call_title: string;
  created_time?: string;
  exchange: 'NASDAQ' | 'NYSE' | 'ASX' | 'TSX' | 'LSE' | 'ALL';
  file?: File;
  file_url?: string;
  symbol: string;
}

export async function uploadConferenceCallAudio({
  call_id,
  call_title,
  created_time,
  exchange,
  file,
  file_url,
  symbol,
}: UploadConferenceCallAudioParams): Promise<{ success: boolean; message: string }> {
  try {
    const formData = new FormData();

    if (call_id) {
      formData.append('call_id', call_id);
    }

    formData.append('call_title', call_title);
    formData.append('exchange', exchange);
    formData.append('symbol', symbol);

    if (created_time) {
      formData.append('created_time', created_time);
    }

    if (file) {
      formData.append('file', file);
    }

    if (file_url) {
      formData.append('file_url', file_url);
    }

    const response = await axios.post(`${global.env.TRANSCRIPT_V1_ENDPOINT}/trigger`, formData, {
      headers: {
        Authorization: `Bearer ${global.env.TRANSCRIPT_V1_TOKEN}`,
        'Content-Type': 'multipart/form-data',
      },
    });

    return {
      message: response.data.message || 'File uploaded successfully',
      success: true,
    };
  } catch (error: any) {
    return {
      message: error.response?.data?.message || 'Failed to upload file',
      success: false,
    };
  }
}
