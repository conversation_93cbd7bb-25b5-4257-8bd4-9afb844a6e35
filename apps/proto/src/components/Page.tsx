import React, { startTransition, useEffect, useState } from 'react';
import Script from 'next/script';
import { MainMenu, NavigationFooter, NavigationHeader } from '@benzinga/navigation-ui';
import { DFPManager } from 'react-dfp';
import { Authentication, AuthenticationManager } from '@benzinga/session';
import { SessionContext } from '@benzinga/session-context';
import { UserManager } from '@benzinga/user-manager';
import { checkDeviceType } from '@benzinga/device-utils';
import { disableOptinMonsterCampaigns } from '@benzinga/ads-utils';
import { ClickTracker, PageTracker, GeoTracker } from '@benzinga/analytics';
import { Meta, MetaProps, Schema } from '@benzinga/seo';
import { AuthPortal, OneSignalScript } from '@benzinga/auth-ui';
import { cookieCleaner, formatNavigationSchema, isLocalStorageAvailable } from '@benzinga/frontend-utils';
import Modal from 'react-modal';
import { useRouter } from 'next/router';
import { TrackingManager } from '@benzinga/tracking-manager';
import { safeDelay } from '@benzinga/safe-await';

Modal.setAppElement('#__next');

if (typeof window !== 'undefined') {
  cookieCleaner();
}

export const addRecentTicker = (ticker: string): void => {
  if (isLocalStorageAvailable()) {
    console.log('update recent quotes');
    const recentTickers = getRecentTickers();
    const index = recentTickers.indexOf(ticker);
    if (index !== -1) {
      recentTickers.splice(index, 1);
    }
    recentTickers.unshift(ticker);
    window?.localStorage?.setItem('recentQuotes', JSON.stringify(recentTickers));
  }
};

export const getRecentTickers = (): string[] => {
  if (!isLocalStorageAvailable()) return [];

  const recentTickerData = window?.localStorage?.getItem('recentQuotes');
  return recentTickerData ? JSON.parse(recentTickerData) : [];
};

const Page = ({
  article,
  children,
  disablePageTracking,
  embeddedWidget,
  error,
  headerProps,
  metaProps,
  page,
  post,
  statusCode,
}) => {
  const [recentTickers, setRecentTickers] = useState<string[]>();

  const router = useRouter();
  const session = React.useContext(SessionContext);
  const authManager = session.getManager(AuthenticationManager);
  const userManager = session.getManager(UserManager);
  const [meta, setMeta] = useState<MetaProps>(metaProps);

  const shouldPageTrack = statusCode !== 404 && !disablePageTracking && error !== 404;

  const getWelcomeUrl = async (): Promise<string> => {
    const userLayoutList = await userManager.getUser()?.layout_list;
    const isUserOnBoarded = await userManager.getGlobalSettings('isUserOnBoarded');

    if (!isUserOnBoarded.ok && !userLayoutList?.length) {
      return `/welcome?next=${encodeURIComponent(window.location.href)}`;
    }
    return '';
  };

  React.useEffect(() => {
    // @ts-expect-error Custom Event
    window.addEventListener('page-transition', (e: CustomEvent) => {
      setMeta(e.detail);
      session.getManager(TrackingManager).setMeta(e.detail);
    });
    return () => {
      // eslint-disable-next-line @typescript-eslint/no-empty-function
      window.removeEventListener('page-transition', () => {});
    };
  }, [session, setMeta]);

  useEffect(() => {
    // Hack to disable all fonts from the Optin Monster
    document.addEventListener('om.Scripts.init', event => {
      if (event['detail']) {
        event['detail'].Scripts.enabled.fonts = false;
      }
    });

    // if (headerProps?.disableOptinMonster || authManager.isLoggedIn()) {
    //   disableOptinMonsterCampaigns();
    // }

    disableOptinMonsterCampaigns();

    const recentTickers = getRecentTickers();
    startTransition(() => {
      setRecentTickers(recentTickers.splice(0, 7));
    });
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  const handleOnRegister = React.useCallback(() => {
    router.push('/welcome');
  }, [router]);

  // @ts-expect-error Not all code paths return a value.
  React.useEffect(() => {
    if (meta) {
      DFPManager.setTargetingArguments({
        BZ_PTYPE: meta?.pageType,
      });
      DFPManager.load();
      return () => {
        DFPManager.setTargetingArguments({});
      };
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  useEffect(() => {
    if (!authManager.isLoggedIn() && !embeddedWidget) {
      const callback = async (auth: Authentication) => {
        if (auth?.user?.accessType !== 'anonymous') {
          try {
            session.getManager(TrackingManager).trackAuthEvent('login', { auth_type: 'google-one-tap' });
            await safeDelay(500);
          } catch (e) {
            console.error('Error tracking one-tap login', e);
          }
          if (window.location?.pathname?.includes('/login') && router.query?.iframe) {
            window.parent.postMessage(
              { next: router.query?.redirect ?? router.query?.next ?? undefined, type: `login_succeed` },
              '*',
            );
          }
        }
        const welcomeUrl = await getWelcomeUrl();
        if (welcomeUrl) {
          router.push(welcomeUrl);
        }
      };
      authManager.showGoogleOneTap(callback);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  const canonical = meta?.canonical ?? `https://www.benzinga.com${router.asPath}`;

  const device = checkDeviceType();
  const shouldLoad = device.isDesktop();

  return (
    <>
      {!embeddedWidget && (
        <>
          <Script async defer key="" src="https://accounts.google.com/gsi/client" type="text/javascript"></Script>
          {shouldLoad && <OneSignalScript />}
        </>
      )}
      <React.Suspense>
        <ClickTracker article={article} post={post || page} />
        <GeoTracker />
        <AuthPortal authMode="register" onRegister={handleOnRegister} />
      </React.Suspense>
      {shouldPageTrack && meta && <PageTracker />}
      {meta && <Meta {...meta} canonical={canonical} />}
      <Schema data={formatNavigationSchema(MainMenu.primary)} name="navigation-header" />

      {!headerProps?.hideNavigationBar && (
        <NavigationHeader
          hideBanner={headerProps?.hideBanner}
          hideQuoteBar={headerProps?.hideQuoteBar}
          logoVariant={headerProps?.logoVariant || 'default'}
          marketTickers={headerProps?.marketTickers}
          menus={MainMenu}
          quotes={headerProps?.quotes}
          recentTickers={recentTickers}
          showAboveHeaderBlock={false}
        />
      )}
      {children}
      {!headerProps?.hideFooter && <NavigationFooter showStickyFooter={headerProps?.showStickyFooter} />}
    </>
  );
};

export default Page;
