import styled from '@benzinga/themetron';
import { absColDef, AdvancedColDef } from '../helpers/advancedHeader';
import { IRowNode, ValueFormatterParams } from '@ag-grid-community/core';
import React from 'react';
import { CustomCellRendererProps } from '@ag-grid-community/react';
import { formatLarge, isNil } from '@benzinga/utils';
import { NO_VALUE } from '../../Ticker';

const ChangePercentRenderer: React.FC<CustomCellRendererProps> = props => {
  const val = props.value;
  const valFormat = props.valueFormatted;

  if (isNil(val) || isNaN(val)) {
    return <Neutral>{NO_VALUE}</Neutral>;
  } else if (val === 0) {
    return <Neutral>{valFormat}</Neutral>;
  } else if (val > 0) {
    return <Positive>{valFormat}</Positive>;
  } else {
    return <Negative>{valFormat}</Negative>;
  }
};

const Neutral = styled.div`
  padding: 0 6px;
`;

const Positive = styled(Neutral)`
  color: ${({ theme }) => theme.colors.statistic.positive} !important;
  fill: ${({ theme }) => theme.colors.statistic.positive} !important;
`;

const Negative = styled(Neutral)`
  color: ${({ theme }) => theme.colors.statistic.negative} !important;
  fill: ${({ theme }) => theme.colors.statistic.negative} !important;
`;

export const ChangePercentColDef = (colDef: AdvancedColDef): AdvancedColDef => ({
  cellRenderer: ChangePercentRenderer,
  cellStyle: { 'text-align': 'right' },

  // renderer handles
  comparator: (_valueA: number, _valueB: number, nodeA: IRowNode, nodeB: IRowNode) =>
    nodeA.data[nodeA.id ?? ''] - nodeB.data[nodeB.id ?? ''],

  filter: 'agNumberColumnFilter',
  ...colDef,
  ...absColDef({
    ...colDef,
    advancedSort: {
      absSort: true,
      val: 'desc-abs',
      ...colDef.advancedSort,
    } as AdvancedColDef['advancedSort'],
  }),
  valueFormatter: ({ value }: ValueFormatterParams) => {
    if (isNil(value) || value === '') return NO_VALUE;
    const val = formatLarge(value).replace(/[a-zA-Z]/g, '');
    if (val.includes('%')) return val;
    return `${val}%`;
  },
});
