# Raptive Ad Manager Debug Logging

## Overview

The `RaptiveAdManager` now includes comprehensive debug logging that captures the state of ad placements in the DOM before the Raptive script gets injected. This is useful for debugging ad loading issues and understanding the placement state.

## When Logging Occurs

The debug logging is triggered right before the Raptive script injection in the `executeLoadAdScript` method:

```typescript
this.log('Injecting Raptive ad script');
this.logPlacementsBeforeScriptInjection(); // <-- New debug logging
injectRaptiveScript({...});
```

## What Gets Logged

### 1. Detailed Placement Information

```javascript
{
  "allRaptiveElementsCount": 4,
  "expectedPlaceholderCount": 3,
  "pageUrl": "https://www.benzinga.com/article/...",
  "placementsInDOM": [
    {
      "childrenCount": 1,
      "className": "raptive-ad-placement mx-auto overflow-hidden min-h-[250px]",
      "hasContent": true,
      "id": "raptive-ad-content-1",
      "index": 1,
      "innerHTML": "<div class=\"raptive-ben-content w-full flex items-center justify-center\" id=\"raptive-ad-content-1\"></div>",
      "isVisible": true,
      "placeholderType": "raptive-ben-content",
      "position": {
        "height": 250,
        "top": 1200,
        "width": 300
      }
    },
    {
      "childrenCount": 0,
      "className": "raptive-ad-placement mx-auto overflow-hidden min-h-[90px]",
      "hasContent": false,
      "id": "raptive-ad-header-1",
      "index": 2,
      "innerHTML": "",
      "isVisible": false,
      "placeholderType": "unknown",
      "position": {
        "height": 0,
        "top": 0,
        "width": 0
      }
    }
  ],
  "registeredPlaceholders": ["raptive-ad-content-1", "raptive-ad-header-1"],
  "registeredPlaceholdersCount": 2,
  "timestamp": "2024-08-02T10:30:45.123Z",
  "totalPlacementsInDOM": 2
}
```

### 2. Summary Log

```
📊 [DEBUG] Summary: 2 placements in DOM, 3 expected, 2 registered, 4 total raptive elements
```

### 3. Warning Logs

When issues are detected:

```
[Raptive Ad Manager] No ad placements found in DOM before script injection
```

```
[Raptive Ad Manager] Found fewer placements (2) than expected (3)
```

### 4. Script Injection Failure Reasons

When the script cannot be injected, detailed reasons are provided:

```
[Raptive Ad Manager] Skipping ad script injection because:
[Raptive Ad Manager]   • Expected (3) placeholders, but found (1) placeholders
[Raptive Ad Manager]   • Registered placeholders (2): raptive-ad-content-1, raptive-ad-header-1
[Raptive Ad Manager]   • Placeholders in DOM (1): raptive-ben-content
```

```
[Raptive Ad Manager] Ads disabled on current page - skipping script injection
```

```
[Raptive Ad Manager] Ad script injection already in progress
```

## Key Information Captured

- **Total placements in DOM**: Number of elements with `.raptive-ad-placement` class
- **Expected placeholder count**: How many placeholders the manager expects
- **Registered placeholders**: Placeholders that have been registered via `registerPlaceholder()`
- **All Raptive elements**: Total count of any element with "raptive" in the class name
- **Individual placement details**:
  - Element ID and class names
  - Whether it has content (children)
  - Position and visibility information
  - Placeholder type (extracted from first child's class)
  - Truncated innerHTML for content inspection

## How to Use for Debugging

1. **Check placement count**: Verify that the expected number of placements are in the DOM
2. **Verify registration**: Ensure registered placeholders match what's in the DOM
3. **Content inspection**: Check if placements have content or are empty
4. **Visibility issues**: Use position data to see if placements are visible
5. **Timing issues**: Use timestamp to understand when injection occurs

## Console Output Example

### Successful Script Injection
```
[Raptive Ad Manager] Initializing RaptiveAdManager
[Raptive Ad Manager] Updated state - count: 3, ready: true
[Raptive Ad Manager] Setting up observer for 3 expected placeholders
[Raptive Ad Manager] Expected placeholders found in DOM, loading ad script
[Raptive Ad Manager] Injecting ad script
[Raptive Ad Manager] Placements before script injection: 3 in DOM, 3 expected, 3 registered
[Raptive Ad Manager] Placement 1: raptive-ad-content-1 (raptive-ben-content) - content: true, visible: true
[Raptive Ad Manager] Placement 2: raptive-ad-header-1 (raptive-ben-header) - content: false, visible: false
[Raptive Ad Manager] Placement 3: raptive-ad-sidebar-1 (raptive-ben-sidebar) - content: true, visible: true
[Raptive Ad Manager] Ad script injected successfully
```

### Script Injection Blocked
```
[Raptive Ad Manager] Initializing RaptiveAdManager
[Raptive Ad Manager] Updated state - count: 3, ready: true
[Raptive Ad Manager] Setting up observer for 3 expected placeholders
[Raptive Ad Manager] Skipping ad script injection because:
[Raptive Ad Manager]   • Expected (3) placeholders, but found (1) placeholders
[Raptive Ad Manager]   • Registered placeholders (2): raptive-ad-content-1, raptive-ad-header-1
[Raptive Ad Manager]   • Placeholders in DOM (1): raptive-ben-content
```

This logging helps identify common issues like:
- Missing ad placements
- Incorrect placeholder registration
- Timing issues with DOM rendering
- CSS visibility problems
- Content loading issues
