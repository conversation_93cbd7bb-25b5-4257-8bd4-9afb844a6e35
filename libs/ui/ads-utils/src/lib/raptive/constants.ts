import type { RaptiveAdPlacementType } from './entities';

// leaderboard/header - .raptive-ben-header
// static sidebar - .raptive-ben-static-sidebar
// sticky sidebar - .raptive-ben-sticky-sidebar
// within content ad - .raptive-ben-content
// below post ad - .raptive-ben-bp
// content home page - .raptive-ben-content-hp
// header small - .raptive-ben-header-sml
// content small - .raptive-ben-content-sml

export const raptiveAdWrapperClassnames: Record<RaptiveAdPlacementType, string> = {
  bottom: 'raptive-ben-bp',
  content: 'raptive-ben-content',
  'content-250': 'raptive-ben-content-250',
  'content-home-page': 'raptive-ben-content-hp',
  'content-small': 'raptive-ben-content-sml',
  header: 'raptive-ben-header',
  'header-90': 'raptive-ben-header-90',
  'header-small': 'raptive-ben-header-sml',
  'static-sidebar': 'raptive-ben-static-sidebar',
  'sticky-sidebar': 'raptive-ben-sticky-sidebar',
};

export const raptiveAdPlaceholderHeightMap: Record<RaptiveAdPlacementType, string> = {
  bottom: 'min-h-[250px]',
  content: 'min-h-[250px]',
  'content-250': 'min-h-[250px]',
  'content-home-page': 'min-h-[250px]',
  'content-small': 'min-h-[100px] max-h-[250px]',
  header: 'min-h-[90px]',
  'header-90': 'min-h-[90px]',
  'header-small': 'min-h-[90px]',
  'static-sidebar': 'min-h-[250px]',
  'sticky-sidebar': 'min-h-[250px]',
};
