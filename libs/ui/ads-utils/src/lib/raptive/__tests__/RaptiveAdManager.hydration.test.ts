/**
 * @jest-environment jsdom
 */

import { raptiveAdManager } from '../RaptiveAdManager';

// Mock the utils
jest.mock('../utils', () => ({
  appendAdLightClassToBody: jest.fn(),
  appendPaywallClassToBody: jest.fn(),
  appendSandboxClassToBody: jest.fn(),
  appendStagingClassToBody: jest.fn(),
  captureEmailFromPageUrl: jest.fn(),
  detectAndRemoveEmailsFromUrl: jest.fn(),
  getReferrerSiteName: jest.fn(() => 'test-referrer'),
  getUtmSourceName: jest.fn(() => 'test-utm'),
  injectRaptiveFooterAdScript: jest.fn(),
  injectRaptiveScript: jest.fn(options => {
    // Simulate successful script injection
    setTimeout(() => options.onLoad(), 100);
  }),
  isPaywallClassAppended: jest.fn(() => false),
  removeRaptiveFooterAdScript: jest.fn(),
}));

// Mock runningClientSide
jest.mock('@benzinga/utils', () => ({
  runningClientSide: () => true,
}));

describe('RaptiveAdManager - Smart Fallback', () => {
  beforeEach(() => {
    // Clear the DOM
    document.body.innerHTML = '';
    jest.clearAllMocks();
    jest.useFakeTimers();

    // Reset the manager state
    (raptiveAdManager as any).registeredPlaceholders = new Set();
    (raptiveAdManager as any).expectedPlaceholderCount = 0;
    (raptiveAdManager as any).IS_SCRIPT_INJECTED = false;
    (raptiveAdManager as any).IS_SCRIPT_INJECTING = false;
    (raptiveAdManager as any).DISABLED = false;
    (raptiveAdManager as any).READY = true;
  });

  afterEach(() => {
    jest.useRealTimers();
    raptiveAdManager.cleanup();
  });

  it('should set up smart fallback on initialization', () => {
    // Set expected placeholders to trigger observer setup
    (raptiveAdManager as any).expectedPlaceholderCount = 2;

    // Create a new manager instance to trigger constructor
    const { RaptiveAdManager } = require('../RaptiveAdManager');
    const manager = new RaptiveAdManager();

    // Verify fallback is scheduled
    expect((manager as any).fallbackScheduled).toBe(true);

    manager.cleanup();
  });

  it('should load ad script via smart fallback when page is already loaded', () => {
    const loadAdScriptSpy = jest.spyOn(raptiveAdManager as any, 'loadAdScript');
    const logSpy = jest.spyOn(raptiveAdManager as any, 'log');

    // Mock document.readyState as 'complete'
    Object.defineProperty(document, 'readyState', {
      value: 'complete',
      writable: true,
    });

    // Set up the manager with expected placeholders but no script injected
    (raptiveAdManager as any).expectedPlaceholderCount = 2;
    (raptiveAdManager as any).setupSmartFallback();

    // Fast-forward time to trigger requestIdleCallback fallback
    jest.advanceTimersByTime(1000);

    // Verify the fallback was triggered
    expect(logSpy).toHaveBeenCalledWith('Loading ad script via smart fallback after page load', 'info');
    expect(loadAdScriptSpy).toHaveBeenCalledWith(true);
  });

  it('should not trigger smart fallback if script is already injected', () => {
    const loadAdScriptSpy = jest.spyOn(raptiveAdManager as any, 'loadAdScript');
    const logSpy = jest.spyOn(raptiveAdManager as any, 'log');

    // Set script as already injected
    (raptiveAdManager as any).IS_SCRIPT_INJECTED = true;
    (raptiveAdManager as any).checkAndLoadFallback();

    // Verify the fallback was not triggered but logged
    expect(logSpy).toHaveBeenCalledWith('Script already loaded/loading, skipping fallback', 'debug');
    expect(loadAdScriptSpy).not.toHaveBeenCalled();
  });

  it('should not trigger smart fallback if script is currently injecting', () => {
    const loadAdScriptSpy = jest.spyOn(raptiveAdManager as any, 'loadAdScript');
    const logSpy = jest.spyOn(raptiveAdManager as any, 'log');

    // Set script as currently injecting
    (raptiveAdManager as any).IS_SCRIPT_INJECTING = true;
    (raptiveAdManager as any).checkAndLoadFallback();

    // Verify the fallback was not triggered but logged
    expect(logSpy).toHaveBeenCalledWith('Script already loaded/loading, skipping fallback', 'debug');
    expect(loadAdScriptSpy).not.toHaveBeenCalled();
  });

  it('should clear fallback state when cleared', () => {
    // Set up smart fallback
    (raptiveAdManager as any).fallbackScheduled = true;

    // Clear the fallback
    (raptiveAdManager as any).clearFallbackTimeout();

    // Verify fallback state was cleared
    expect((raptiveAdManager as any).fallbackScheduled).toBe(false);
  });

  it('should clear fallback on cleanup', () => {
    const clearFallbackSpy = jest.spyOn(raptiveAdManager as any, 'clearFallbackTimeout');

    // Set up smart fallback
    (raptiveAdManager as any).setupSmartFallback();

    // Call cleanup
    raptiveAdManager.cleanup();

    // Verify fallback was cleared
    expect(clearFallbackSpy).toHaveBeenCalled();
  });

  it('should wait for page load event when document is not ready', () => {
    const addEventListenerSpy = jest.spyOn(window, 'addEventListener');
    const logSpy = jest.spyOn(raptiveAdManager as any, 'log');

    // Mock document.readyState as 'loading'
    Object.defineProperty(document, 'readyState', {
      value: 'loading',
      writable: true,
    });

    // Set up smart fallback
    (raptiveAdManager as any).setupSmartFallback();

    // Verify it waits for load event
    expect(logSpy).toHaveBeenCalledWith('Waiting for page load to schedule fallback', 'debug');
    expect(addEventListenerSpy).toHaveBeenCalledWith('load', expect.any(Function), { once: true });

    addEventListenerSpy.mockRestore();
  });
});
