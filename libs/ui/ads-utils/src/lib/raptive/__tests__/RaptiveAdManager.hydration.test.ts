/**
 * @jest-environment jsdom
 */

import { raptiveAdManager } from '../RaptiveAdManager';

// Mock the utils
jest.mock('../utils', () => ({
  appendAdLightClassToBody: jest.fn(),
  appendPaywallClassToBody: jest.fn(),
  appendSandboxClassToBody: jest.fn(),
  appendStagingClassToBody: jest.fn(),
  captureEmailFromPageUrl: jest.fn(),
  detectAndRemoveEmailsFromUrl: jest.fn(),
  getReferrerSiteName: jest.fn(() => 'test-referrer'),
  getUtmSourceName: jest.fn(() => 'test-utm'),
  injectRaptiveFooterAdScript: jest.fn(),
  injectRaptiveScript: jest.fn(options => {
    // Simulate successful script injection
    setTimeout(() => options.onLoad(), 100);
  }),
  isPaywallClassAppended: jest.fn(() => false),
  removeRaptiveFooterAdScript: jest.fn(),
}));

// Mock runningClientSide
jest.mock('@benzinga/utils', () => ({
  runningClientSide: () => true,
}));

describe('RaptiveAdManager - Fallback Timeout', () => {
  beforeEach(() => {
    // Clear the DOM
    document.body.innerHTML = '';
    jest.clearAllMocks();
    jest.useFakeTimers();

    // Reset the manager state
    (raptiveAdManager as any).registeredPlaceholders = new Set();
    (raptiveAdManager as any).expectedPlaceholderCount = 0;
    (raptiveAdManager as any).IS_SCRIPT_INJECTED = false;
    (raptiveAdManager as any).IS_SCRIPT_INJECTING = false;
    (raptiveAdManager as any).DISABLED = false;
    (raptiveAdManager as any).READY = true;
  });

  afterEach(() => {
    jest.useRealTimers();
    raptiveAdManager.cleanup();
  });

  it('should set up fallback timeout on initialization', () => {
    // Set expected placeholders to trigger observer setup
    (raptiveAdManager as any).expectedPlaceholderCount = 2;

    // Create a new manager instance to trigger constructor
    const { RaptiveAdManager } = require('../RaptiveAdManager');
    const manager = new RaptiveAdManager();

    // Verify fallback timeout is set
    expect((manager as any).fallbackTimeout).toBeTruthy();

    manager.cleanup();
  });

  it('should load ad script via fallback timeout after 3 seconds', () => {
    const loadAdScriptSpy = jest.spyOn(raptiveAdManager as any, 'loadAdScript');
    const logSpy = jest.spyOn(raptiveAdManager as any, 'log');

    // Set up the manager with expected placeholders but no script injected
    (raptiveAdManager as any).expectedPlaceholderCount = 2;
    (raptiveAdManager as any).setupFallbackTimeout();

    // Fast-forward time by 3 seconds
    jest.advanceTimersByTime(3000);

    // Verify the fallback was triggered
    expect(logSpy).toHaveBeenCalledWith('Loading ad script via fallback timeout after 3 seconds', 'info');
    expect(loadAdScriptSpy).toHaveBeenCalledWith(true);
  });

  it('should not trigger fallback timeout if script is already injected', () => {
    const loadAdScriptSpy = jest.spyOn(raptiveAdManager as any, 'loadAdScript');

    // Set script as already injected
    (raptiveAdManager as any).IS_SCRIPT_INJECTED = true;
    (raptiveAdManager as any).setupFallbackTimeout();

    // Fast-forward time by 3 seconds
    jest.advanceTimersByTime(3000);

    // Verify the fallback was not triggered
    expect(loadAdScriptSpy).not.toHaveBeenCalled();
  });

  it('should not trigger fallback timeout if script is currently injecting', () => {
    const loadAdScriptSpy = jest.spyOn(raptiveAdManager as any, 'loadAdScript');

    // Set script as currently injecting
    (raptiveAdManager as any).IS_SCRIPT_INJECTING = true;
    (raptiveAdManager as any).setupFallbackTimeout();

    // Fast-forward time by 3 seconds
    jest.advanceTimersByTime(3000);

    // Verify the fallback was not triggered
    expect(loadAdScriptSpy).not.toHaveBeenCalled();
  });

  it('should clear fallback timeout when script loads successfully', () => {
    const clearTimeoutSpy = jest.spyOn(global, 'clearTimeout');

    // Set up fallback timeout
    (raptiveAdManager as any).setupFallbackTimeout();
    const timeoutId = (raptiveAdManager as any).fallbackTimeout;

    // Clear the fallback timeout
    (raptiveAdManager as any).clearFallbackTimeout();

    // Verify timeout was cleared
    expect(clearTimeoutSpy).toHaveBeenCalledWith(timeoutId);
    expect((raptiveAdManager as any).fallbackTimeout).toBeNull();
  });

  it('should clear fallback timeout on cleanup', () => {
    const clearFallbackSpy = jest.spyOn(raptiveAdManager as any, 'clearFallbackTimeout');

    // Set up fallback timeout
    (raptiveAdManager as any).setupFallbackTimeout();

    // Call cleanup
    raptiveAdManager.cleanup();

    // Verify fallback timeout was cleared
    expect(clearFallbackSpy).toHaveBeenCalled();
  });
});
