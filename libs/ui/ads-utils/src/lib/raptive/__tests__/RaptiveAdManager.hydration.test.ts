/**
 * @jest-environment jsdom
 */

import { raptiveAdManager } from '../RaptiveAdManager';

// Mock the utils
jest.mock('../utils', () => ({
  appendAdLightClassToBody: jest.fn(),
  appendPaywallClassToBody: jest.fn(),
  appendSandboxClassToBody: jest.fn(),
  appendStagingClassToBody: jest.fn(),
  captureEmailFromPageUrl: jest.fn(),
  detectAndRemoveEmailsFromUrl: jest.fn(),
  getReferrerSiteName: jest.fn(() => 'test-referrer'),
  getUtmSourceName: jest.fn(() => 'test-utm'),
  injectRaptiveFooterAdScript: jest.fn(),
  injectRaptiveScript: jest.fn((options) => {
    // Simulate successful script injection
    setTimeout(() => options.onLoad(), 100);
  }),
  isPaywallClassAppended: jest.fn(() => false),
  removeRaptiveFooterAdScript: jest.fn(),
}));

// Mock runningClientSide
jest.mock('@benzinga/utils', () => ({
  runningClientSide: () => true,
}));

describe('RaptiveAdManager - Hydration Fallback', () => {
  beforeEach(() => {
    // Clear the DOM
    document.body.innerHTML = '';
    jest.clearAllMocks();
    jest.useFakeTimers();
    
    // Reset the manager state
    (raptiveAdManager as any).registeredPlaceholders = new Set();
    (raptiveAdManager as any).expectedPlaceholderCount = 0;
    (raptiveAdManager as any).IS_SCRIPT_INJECTED = false;
    (raptiveAdManager as any).IS_SCRIPT_INJECTING = false;
    (raptiveAdManager as any).DISABLED = false;
    (raptiveAdManager as any).READY = true;
  });

  afterEach(() => {
    jest.useRealTimers();
    raptiveAdManager.cleanup();
  });

  it('should set up hydration fallback timeout on initialization', () => {
    // Set expected placeholders to trigger observer setup
    (raptiveAdManager as any).expectedPlaceholderCount = 2;
    
    // Create a new manager instance to trigger constructor
    const { RaptiveAdManager } = require('../RaptiveAdManager');
    const manager = new RaptiveAdManager();
    
    // Verify hydration fallback timeout is set
    expect((manager as any).hydrationFallbackTimeout).toBeTruthy();
    
    manager.cleanup();
  });

  it('should load ad script via hydration fallback after 3 seconds', () => {
    const loadAdScriptSpy = jest.spyOn(raptiveAdManager as any, 'loadAdScript');
    const logSpy = jest.spyOn(raptiveAdManager as any, 'log');
    
    // Set up the manager with expected placeholders but no script injected
    (raptiveAdManager as any).expectedPlaceholderCount = 2;
    (raptiveAdManager as any).setupHydrationFallback();
    
    // Fast-forward time by 3 seconds
    jest.advanceTimersByTime(3000);
    
    // Verify the fallback was triggered
    expect(logSpy).toHaveBeenCalledWith('Loading ad script via hydration fallback after 3 seconds', 'info');
    expect(loadAdScriptSpy).toHaveBeenCalledWith(true);
  });

  it('should not trigger hydration fallback if script is already injected', () => {
    const loadAdScriptSpy = jest.spyOn(raptiveAdManager as any, 'loadAdScript');
    
    // Set script as already injected
    (raptiveAdManager as any).IS_SCRIPT_INJECTED = true;
    (raptiveAdManager as any).setupHydrationFallback();
    
    // Fast-forward time by 3 seconds
    jest.advanceTimersByTime(3000);
    
    // Verify the fallback was not triggered
    expect(loadAdScriptSpy).not.toHaveBeenCalled();
  });

  it('should not trigger hydration fallback if script is currently injecting', () => {
    const loadAdScriptSpy = jest.spyOn(raptiveAdManager as any, 'loadAdScript');
    
    // Set script as currently injecting
    (raptiveAdManager as any).IS_SCRIPT_INJECTING = true;
    (raptiveAdManager as any).setupHydrationFallback();
    
    // Fast-forward time by 3 seconds
    jest.advanceTimersByTime(3000);
    
    // Verify the fallback was not triggered
    expect(loadAdScriptSpy).not.toHaveBeenCalled();
  });

  it('should clear hydration fallback timeout when script loads successfully', () => {
    const clearTimeoutSpy = jest.spyOn(global, 'clearTimeout');
    
    // Set up hydration fallback
    (raptiveAdManager as any).setupHydrationFallback();
    const timeoutId = (raptiveAdManager as any).hydrationFallbackTimeout;
    
    // Clear the fallback timeout
    (raptiveAdManager as any).clearHydrationFallbackTimeout();
    
    // Verify timeout was cleared
    expect(clearTimeoutSpy).toHaveBeenCalledWith(timeoutId);
    expect((raptiveAdManager as any).hydrationFallbackTimeout).toBeNull();
  });

  it('should clear hydration fallback timeout on cleanup', () => {
    const clearHydrationFallbackSpy = jest.spyOn(raptiveAdManager as any, 'clearHydrationFallbackTimeout');
    
    // Set up hydration fallback
    (raptiveAdManager as any).setupHydrationFallback();
    
    // Call cleanup
    raptiveAdManager.cleanup();
    
    // Verify hydration fallback was cleared
    expect(clearHydrationFallbackSpy).toHaveBeenCalled();
  });
});
