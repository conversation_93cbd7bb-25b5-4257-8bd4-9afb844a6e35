/**
 * @jest-environment jsdom
 */

import { raptiveAdManager } from '../RaptiveAdManager';

// Mock the utils
jest.mock('../utils', () => ({
  appendAdLightClassToBody: jest.fn(),
  appendPaywallClassToBody: jest.fn(),
  appendSandboxClassToBody: jest.fn(),
  appendStagingClassToBody: jest.fn(),
  captureEmailFromPageUrl: jest.fn(),
  detectAndRemoveEmailsFromUrl: jest.fn(),
  getReferrerSiteName: jest.fn(() => 'test-referrer'),
  getUtmSourceName: jest.fn(() => 'test-utm'),
  injectRaptiveFooterAdScript: jest.fn(),
  injectRaptiveScript: jest.fn(),
  isPaywallClassAppended: jest.fn(() => false),
  removeRaptiveFooterAdScript: jest.fn(),
}));

// Mock runningClientSide
jest.mock('@benzinga/utils', () => ({
  runningClientSide: () => true,
}));

describe('RaptiveAdManager - logPlacementsBeforeScriptInjection', () => {
  beforeEach(() => {
    // Clear the DOM
    document.body.innerHTML = '';
    jest.clearAllMocks();
    
    // Reset the manager state
    (raptiveAdManager as any).registeredPlaceholders = new Set();
    (raptiveAdManager as any).expectedPlaceholderCount = 0;
  });

  it('should log placement information when placements exist in DOM', () => {
    // Create mock ad placements
    const placement1 = document.createElement('div');
    placement1.className = 'raptive-ad-placement';
    placement1.id = 'raptive-ad-content-1';
    placement1.innerHTML = '<div class="raptive-ben-content">Ad content here</div>';
    
    const placement2 = document.createElement('div');
    placement2.className = 'raptive-ad-placement';
    placement2.id = 'raptive-ad-header-1';
    placement2.innerHTML = '<div class="raptive-ben-header">Header ad</div>';
    
    document.body.appendChild(placement1);
    document.body.appendChild(placement2);

    // Set up some registered placeholders
    (raptiveAdManager as any).registeredPlaceholders.add('raptive-ad-content-1');
    (raptiveAdManager as any).registeredPlaceholders.add('raptive-ad-header-1');
    (raptiveAdManager as any).expectedPlaceholderCount = 2;

    // Spy on the log method
    const logSpy = jest.spyOn(raptiveAdManager as any, 'log');

    // Call the method
    raptiveAdManager.logPlacementsBeforeScriptInjection();

    // Verify logging was called
    expect(logSpy).toHaveBeenCalledWith(
      '🔍 [DEBUG] Placements in DOM before script injection:',
      'custom',
      expect.objectContaining({
        totalPlacementsInDOM: 2,
        expectedPlaceholderCount: 2,
        registeredPlaceholdersCount: 2,
        allRaptiveElementsCount: expect.any(Number),
        pageUrl: expect.any(String),
        timestamp: expect.any(String),
        placementsInDOM: expect.arrayContaining([
          expect.objectContaining({
            id: 'raptive-ad-content-1',
            className: 'raptive-ad-placement',
            childrenCount: 1,
            hasContent: true,
            index: 1,
          }),
          expect.objectContaining({
            id: 'raptive-ad-header-1',
            className: 'raptive-ad-placement',
            childrenCount: 1,
            hasContent: true,
            index: 2,
          }),
        ]),
      })
    );

    // Verify summary log
    expect(logSpy).toHaveBeenCalledWith(
      expect.stringContaining('📊 [DEBUG] Summary: 2 placements in DOM, 2 expected, 2 registered'),
    );
  });

  it('should log warning when no placements found', () => {
    // No placements in DOM
    (raptiveAdManager as any).expectedPlaceholderCount = 2;

    const logSpy = jest.spyOn(raptiveAdManager as any, 'log');

    raptiveAdManager.logPlacementsBeforeScriptInjection();

    // Should log warning about no placements
    expect(logSpy).toHaveBeenCalledWith(
      '⚠️ [DEBUG] WARNING: No ad placements found in DOM before script injection!',
      'warn'
    );
  });

  it('should log warning when fewer placements than expected', () => {
    // Create only one placement but expect two
    const placement = document.createElement('div');
    placement.className = 'raptive-ad-placement';
    placement.id = 'raptive-ad-content-1';
    document.body.appendChild(placement);

    (raptiveAdManager as any).expectedPlaceholderCount = 2;

    const logSpy = jest.spyOn(raptiveAdManager as any, 'log');

    raptiveAdManager.logPlacementsBeforeScriptInjection();

    // Should log warning about fewer placements
    expect(logSpy).toHaveBeenCalledWith(
      '⚠️ [DEBUG] WARNING: Found fewer placements (1) than expected (2)',
      'warn'
    );
  });
});
