import React from 'react';
import { Button, ButtonSize, ButtonVariant } from '@benzinga/core-ui';
import styled from '@benzinga/themetron';
import { TrackingManager } from '@benzinga/tracking-manager';
import classNames from 'classnames';
import i18n from '@benzinga/translate';
import { useTranslation } from 'react-i18next';
import { SessionContext } from '@benzinga/session-context';

export interface GetReportButtonProps {
  className?: string;
  ticker: string;
  size?: ButtonSize;
  targetElement?: JSX.Element | null;
  variant?: ButtonVariant;
  onClick?: (e: React.MouseEvent<HTMLButtonElement | HTMLDivElement>, id: string) => void;
}

export const GetReportButton: React.FC<GetReportButtonProps> = ({
  className,
  onClick,
  size,
  targetElement = null,
  ticker,
  variant,
}) => {
  const session = React.useContext(SessionContext);
  const { t } = useTranslation('common', { i18n });

  const openReport = () => {
    window.open(`/reports/${ticker}`, 'report');
  };

  const handleClickButton = (event: React.MouseEvent<HTMLButtonElement | HTMLDivElement>) => {
    session.getManager(TrackingManager).trackLinkEvent('click', {
      link_action: 'open_report',
      link_id: `get_report_${ticker}`,
      link_type: 'get_report_button',
      value: ticker,
    });
    onClick && onClick(event, 'Get Report Button');
    openReport();
  };

  return (
    <Container className={classNames('get-report-button flex', { [`${className}`]: !!className })}>
      {targetElement ? (
        <div onClick={handleClickButton}>{targetElement}</div>
      ) : (
        <Button onClick={handleClickButton} size={size} variant={variant || 'warning'}>
          {t('Buttons.get-report')}
        </Button>
      )}
    </Container>
  );
};

const Container = styled.div`
  &.get-report-button {
    button {
      width: 100%;
    }
  }
`;

export default GetReportButton;
