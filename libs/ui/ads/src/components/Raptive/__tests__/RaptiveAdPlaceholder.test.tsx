import React from 'react';
import { render } from '@testing-library/react';
import { RaptiveAdPlaceholder } from '../RaptiveAdPlaceholder';

// Mock dependencies
jest.mock('@benzinga/edge', () => ({
  useBenzingaEdge: () => ({ disabled: { raptive: false } }),
}));

jest.mock('@benzinga/device-utils', () => ({
  isDesktop: () => true,
  isMobile: () => false,
}));

jest.mock('@benzinga/ads-utils', () => ({
  raptiveAdWrapperClassnames: {
    content: 'raptive-ben-content',
  },
  raptiveAdPlaceholderHeightMap: {
    content: 'min-h-[250px]',
  },
  raptiveAdManager: {
    registerPlaceholder: jest.fn(() => 'test-ad-id'),
    unregisterPlaceholder: jest.fn(),
  },
}));

jest.mock('@benzinga/hooks', () => {
  const React = require('react');
  return {
    NoFirstRender: ({ children, fallback }: { children: React.ReactNode; fallback: React.ReactNode }) => {
      // For testing, we'll render the fallback on first render to simulate SSR
      const [isFirstRender, setIsFirstRender] = React.useState(true);

      React.useEffect(() => {
        setIsFirstRender(false);
      }, []);

      return <>{isFirstRender ? fallback : children}</>;
    },
  };
});

describe('RaptiveAdPlaceholder', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('renders without device restrictions', () => {
    const { container } = render(<RaptiveAdPlaceholder type="content" />);

    expect(container.firstChild).toBeTruthy();
  });

  it('uses NoFirstRender for onlyDesktop prop to prevent hydration mismatch', () => {
    const { container } = render(<RaptiveAdPlaceholder type="content" onlyDesktop={true} />);

    // Should render the fallback div initially (simulating SSR)
    const fallbackDiv = container.querySelector('div');
    expect(fallbackDiv).toHaveClass('min-w-[300px]', 'w-[300px]', 'min-h-[250px]', 'raptive-ad-placement');
  });

  it('uses NoFirstRender for onlyMobile prop to prevent hydration mismatch', () => {
    const { container } = render(<RaptiveAdPlaceholder type="content" onlyMobile={true} />);

    // Should render the fallback div initially (simulating SSR)
    const fallbackDiv = container.querySelector('div');
    expect(fallbackDiv).toHaveClass('min-w-[300px]', 'w-[300px]', 'min-h-[250px]', 'raptive-ad-placement');
  });

  it('uses NoFirstRender for onlyClient prop to prevent hydration mismatch', () => {
    const { container } = render(<RaptiveAdPlaceholder type="content" onlyClient={true} />);

    // Should render the fallback div initially (simulating SSR)
    const fallbackDiv = container.querySelector('div');
    expect(fallbackDiv).toHaveClass('min-w-[300px]', 'w-[300px]', 'min-h-[250px]', 'raptive-ad-placement');
  });

  it('includes custom className in fallback', () => {
    const { container } = render(<RaptiveAdPlaceholder type="content" onlyDesktop={true} className="custom-class" />);

    const fallbackDiv = container.querySelector('div');
    expect(fallbackDiv).toHaveClass('custom-class');
  });

  it('returns empty div when disabled', () => {
    // Mock disabled state
    jest.doMock('@benzinga/edge', () => ({
      useBenzingaEdge: () => ({ disabled: { raptive: true } }),
    }));

    const { RaptiveAdPlaceholder: DisabledComponent } = require('../RaptiveAdPlaceholder');
    const { container } = render(<DisabledComponent type="content" />);

    expect(container.firstChild).toBeInstanceOf(HTMLDivElement);
    expect(container.firstChild?.textContent).toBe('');
  });
});
