import React from 'react';
import { type RaptiveAdPlacementType } from '@benzinga/ads-utils';
import { appEnvironment, appName } from '@benzinga/utils';

const RaptiveAdPlaceholder = React.lazy(() =>
  import('@benzinga/ads').then(module => {
    return { default: module.RaptiveAdPlaceholder };
  }),
);

export interface RaptiveAdPlaceholderBlockProps {
  attrs: {
    className?: string;
    data: {
      ad_light_hidden?: boolean;
      only_client_side?: boolean;
      minimum_height_enabled?: boolean;
      only_desktop?: boolean;
      only_mobile?: boolean;
      type: RaptiveAdPlacementType;
      no_wrapper?: boolean;
    };
  };
}

export const RaptiveAdPlaceholderBlock: React.FC<RaptiveAdPlaceholderBlockProps> = ({ attrs }) => {
  const isBzApp = appEnvironment().isApp(appName.bz);
  if (!isBzApp) return null;
  //const onlyClient = attrs?.data?.only_client_side;
  const onlyDesktop = attrs?.data?.only_desktop;
  const onlyMobile = attrs?.data?.only_mobile;
  const type = attrs?.data?.type;
  const minimumHeightEnabled = attrs?.data?.minimum_height_enabled ?? true;
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  const noWrapper = attrs?.data?.no_wrapper;
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  const adLightHidden = attrs?.data?.ad_light_hidden;

  return (
    <React.Suspense fallback={<div className="h-[250px] w-[300px]" />}>
      <MemoizedRaptiveAdPlaceholder
        adLightHidden={adLightHidden}
        className={attrs?.className}
        //onlyClient={onlyClient}
        minimumHeightEnabled={minimumHeightEnabled}
        noWrapper={noWrapper}
        //onlyDesktop={onlyDesktop}
        //onlyMobile={onlyMobile}
        type={type}
      />
    </React.Suspense>
  );
};

const MemoizedRaptiveAdPlaceholder = React.memo(RaptiveAdPlaceholder);
