'use client';
import React, { createContext, startTransition, useContext, useEffect } from 'react';
import { raptiveAdManager } from '@benzinga/ads-utils';
import { SessionContext } from '@benzinga/session-context';
import { AuthenticationManager } from '@benzinga/session';
import { PermissionsManager } from '@benzinga/permission-manager';
import { appEnvironment, appName } from '@benzinga/utils';
import { isMobile } from '@benzinga/device-utils';

interface DisabledAds {
  raptive: boolean;
  connatix: boolean;
}

interface BenzingaEdgeContextInterface {
  adLightEnabled: boolean;
  disabled: DisabledAds;
  isLoading: boolean;
}

const BenzingaEdgeContext = createContext<BenzingaEdgeContextInterface | null>(null);

interface BenzingaEdgeProviderProps {
  children: React.ReactNode;
  initialTargeting?: Record<string, string | string[]>;
  disableRaptiveReadyOnPageLoad?: boolean;
}

export const BenzingaEdgeProvider: React.FC<BenzingaEdgeProviderProps> = ({
  children,
  disableRaptiveReadyOnPageLoad,
  initialTargeting,
}) => {
  const session = React.useContext(SessionContext);
  const authManager = session.getManager(AuthenticationManager);
  const [hasAdLight, setHasAdLight] = React.useState(false);
  const [isConnatixDisabled, setIsConnatixDisabled] = React.useState(false);
  const [isRaptiveDisabled, setIsRaptiveDisabled] = React.useState(false);
  const [isLoading, setIsLoading] = React.useState(true);

  useEffect(() => {
    // if (initialTargeting && Object.keys(initialTargeting).length > 0) {
    //   raptiveAdManager.setTargeting(initialTargeting);
    // }
    raptiveAdManager.setReady(false);
    raptiveAdManager.init(initialTargeting);

    const handleEnableRaptiveAds = () => {
      !disableRaptiveReadyOnPageLoad && raptiveAdManager.setReady(true);
    };

    const initialize = async () => {
      try {
        const cachedAdLightStatus = localStorage.getItem('adlight');

        if (cachedAdLightStatus !== null) {
          const cachedHasAccess = cachedAdLightStatus === 'true';
          applyAdLightSettings(cachedHasAccess);

          authManager.getAuthSession().then(() => {
            const permissionsManager = session.getManager(PermissionsManager);
            const hasAccess = !!permissionsManager.hasAccess('com/ad-block', 'light')?.ok;
            if (hasAccess !== cachedHasAccess) {
              localStorage.setItem('adlight', String(hasAccess));
              applyAdLightSettings(hasAccess);
            }
          });
        } else {
          await authManager.getAuthSession();
          const permissionsManager = session.getManager(PermissionsManager);
          const hasAccess = !!permissionsManager.hasAccess('com/ad-block', 'light')?.ok;
          localStorage.setItem('adlight', String(hasAccess));
          applyAdLightSettings(hasAccess);
        }
      } catch (error) {
        applyAdLightSettings(false);
      }
    };

    const applyAdLightSettings = (hasAccess: boolean) => {
      startTransition(() => {
        setHasAdLight(hasAccess);
        setIsConnatixDisabled(hasAccess);
        hasAccess && raptiveAdManager.enableAdLightMode();
        setIsRaptiveDisabled(false);
        handleEnableRaptiveAds();
        setIsLoading(false);
      });
    };

    //handleEnableRaptiveAds();
    initialize();
    // if (isMobile()) {
    //   window.addEventListener(
    //     'touchstart',
    //     () => {
    //       initialize();
    //     },
    //     { once: true },
    //   );
    // } else {
    //   window.addEventListener(
    //     'mousemove',
    //     () => {
    //       initialize();
    //     },
    //     { once: true },
    //   );
    // }

    return () => raptiveAdManager.cleanup();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  const disabled = {
    connatix: isConnatixDisabled,
    raptive: isRaptiveDisabled,
  };

  return (
    <BenzingaEdgeContext.Provider value={{ adLightEnabled: hasAdLight, disabled, isLoading }}>
      {children}
    </BenzingaEdgeContext.Provider>
  );
};

export const useBenzingaEdge = () => {
  const isBzApp = appEnvironment().isApp(appName.bz);
  const context = useContext(BenzingaEdgeContext);
  if (!isBzApp || !context) {
    return {
      adLightEnabled: false,
      disabled: {
        connatix: false,
        raptive: false,
      },
      isLoading: false,
    };
  }
  if (context === null) {
    console.error('useBenzingaEdge must be used within a BenzingaEdgeProvider');
  }
  return context;
};
