import { Link, LinkGroupBlockProps } from '@benzinga/blocks';
import { deepArrayInnerBlocksSearch, loadServerSideBlockData } from '@benzinga/blocks-utils';

import { ContentManager } from '@benzinga/content-manager';
import { StoryObject, nodeHasChannel } from '@benzinga/advanced-news-manager';
import { BasicNewsManager } from '@benzinga/basic-news-manager';
import {
  ArticleDFPTags,
  ArticleManager,
  Campaigns,
  getDFPTargeting,
  getTemplateFeatures,
  TemplateOverride,
} from '@benzinga/article-manager';
import { appEnvironment, appName, capitalize } from '@benzinga/utils';

import { SafePromise, safeErrorStatus, safeTimeout } from '@benzinga/safe-await';

import {
  ArticleBlock,
  ArticleData,
  canArticleBeMarkedAsNoIndex,
  shouldDisableCampaignifyUnit,
  getArticleKeywordsGroup,
  getCanonicalUrl,
  getPrimaryTickers,
  getWordsAndCharactersCount,
  isStoryTaggedPressRelease,
} from '@benzinga/article-manager';

import { translatedContentLanguages } from '@benzinga/internal-news-manager';
import { NavigationHeaderProps } from '@benzinga/navigation-ui';
import { Session } from '@benzinga/session';
import { DeviceInfo } from '@benzinga/device-utils';
import { ArticlePageProps } from './entities';
import { QuotesManager, RankingDetail } from '@benzinga/quotes-manager';
import { Candle, ChartManager, CryptoChartResponseResult } from '@benzinga/chart-manager';
import { lensPath, view } from 'ramda';
import { ArticleFeaturedTicker } from '@benzinga/article';
import { DateTime } from 'luxon';

export interface ArticleCampaignSettingsI {
  disableCampaignifyUnit: boolean;
  googleAdPlaceholder?: {
    attrs: {
      data: {
        id: string;
        slot_height: number;
        slot_placement_id: string;
        slot_width: number;
      };
    };
    blockName: string;
  };
}

export interface ArticleServerSideProps extends ArticlePageProps {
  article?: ArticleData | null;
  errorCode?: number;
  baseUrl?: string;
  disablePageTracking?: boolean;
  commentCount?: number;
  headerProps?: NavigationHeaderProps;
  isTaggedPressRelease?: boolean;
  pressReleasesByAuthor?: StoryObject[];
  tickers?: string[];
  pageTargeting?: ArticleDFPTags | null;
}

export const getArticleServerSideProps = async (
  session: Session,
  nid: string | string,
  url: string | string,
  deviceInfo: DeviceInfo,
  getArticleData: (nid: number | string) => SafePromise<ArticleData>,
  type: string | null = null,
  baseUrl: string | null = null,
  template: TemplateOverride = null,
): Promise<{ props: ArticleServerSideProps; statusCode: number }> => {
  const basicNewsManager = session.getManager(BasicNewsManager);
  const articleManager = session.getManager(ArticleManager);

  if (url) {
    try {
      const expiredUrlCheck = await articleManager.checkExpiredUrl('article', url);
      if (expiredUrlCheck.ok?.status === 'success' && expiredUrlCheck.ok?.data) {
        return {
          props: {
            article: null,
            deviceType: deviceInfo.deviceType ?? null,
            disablePageTracking: true,
            errorCode: 410,
            layout: null,
            metaProps: null,
            nid: nid,
          },
          statusCode: 410,
        };
      }
    } catch (error) {
      console.warn('Editorial Tools Expired URLs API error:', error);
    }
  }

  const [articleRes] = await Promise.all([nid && nid !== 'undefined' ? getArticleData(`${nid}`) : null]);
  const article = (articleRes && articleRes?.ok) ?? null;

  const templateFeatures = getTemplateFeatures(nid, template);
  const isBzApp = appEnvironment().isApp(appName.bz);

  const responseCode = (articleRes?.err?.data as safeErrorStatus)?.status;
  const formattedStatusCode = responseCode && responseCode >= 400 ? responseCode : 503;

  if (responseCode && responseCode >= 400 && responseCode < 600 && responseCode !== 404) {
    return {
      props: {
        article: null,
        deviceType: deviceInfo.deviceType ?? null,
        disablePageTracking: true,
        errorCode: formattedStatusCode,
        layout: null,
        metaProps: null,
        nid: nid,
      },
      statusCode: formattedStatusCode,
    };
  }

  //console.log(article);

  // 404 if sec is old and has content path, resolves CPU overload
  const isArticleAnOldSecPr = article?.type === 'pr_secfilings' && url?.includes('/content');

  if (!article || (type && type !== article?.type) || isArticleAnOldSecPr) {
    return {
      props: {
        article: null,
        deviceType: deviceInfo.deviceType ?? null,
        disablePageTracking: true,
        layout: null,
        metaProps: null,
        nid: nid,
      },
      statusCode: 404,
    };
  } else if (articleRes?.err) {
    console.error('the article error:', articleRes.err);

    return {
      props: {
        article: null,
        deviceType: deviceInfo.deviceType ?? null,
        disablePageTracking: true,
        layout: null,
        metaProps: null,
        nid: nid,
      },
      statusCode: 503,
    };
  }

  //const breakingNews = (await session.getManager(BasicNewsManager).getBreakingNewsFormatted())?.ok;
  const isCryptocurrencyChannel = nodeHasChannel(article as unknown as StoryObject, 'Cryptocurrency');

  const headerProps: NavigationHeaderProps = {
    //breakingNews,
    //disableOptinMonster: true,
    hideMobileAppBannerAd: true,
    logoVariant: isCryptocurrencyChannel ? 'crypto' : 'default',
    showRaptiveBanner: templateFeatures.useNewTemplate,
    showRotatingBanner: !templateFeatures.useNewTemplate,
  };

  const contentManager = session.getManager(ContentManager);

  const node_id = nid as string;
  const getLayoutReq = contentManager.getNodeLayout(node_id);
  const layoutRes = await safeTimeout(getLayoutReq, 3000);
  const layout = layoutRes?.ok ?? null;
  article.layout = layout;

  if (Array.isArray(article.layout?.sidebar?.blocks)) {
    article.layout.sidebar.blocks = await loadServerSideBlockData(session, article.layout.sidebar.blocks);
    if (templateFeatures.useNewTemplate) {
      // TODO: Remove this once the new template is officially launched
      article.layout.sidebar.blocks = article.layout.sidebar.blocks.filter(block =>
        ['acf/link-group', 'acf/news-feed'].includes(block.blockName),
      );
    }
  }

  if (Array.isArray(article.layout?.in_content?.blocks)) {
    const inContentBlocks = article.layout.in_content.blocks;
    if (Array.isArray(article.blocks) && article.blocks.length > 0) {
      //if h2 exists then append right before it
      const firstHeadingIndex = article.blocks.findIndex(block => block?.tag === 'h2' || block?.tag === 'h3');
      if (firstHeadingIndex && firstHeadingIndex > 0) {
        article.blocks.splice(firstHeadingIndex, 0, ...(inContentBlocks as ArticleBlock[]));
      } else {
        const index = Math.floor(article.blocks.length / 2);
        article.blocks.splice(index, 0, ...(inContentBlocks as ArticleBlock[]));
      }
    }
  }

  let campaigns: Campaigns | null = null;
  if (templateFeatures.useNewTemplate && article.nodeId) {
    const res = await session.getManager(ArticleManager).getCampaigns(article.nodeId);
    if (res.ok) {
      campaigns = res.ok;
    }
  }

  try {
    const translationResponse = await contentManager.getNodeTranslation(node_id);
    if (translationResponse?.ok) {
      const { data, status } = translationResponse.ok;

      if (status === 'ok' && data) {
        if (data.translations) {
          article.translationMeta = data.translations;
        }
      }
    }
  } catch (e) {
    console.error('ARTICLE TRANSLATION ERROR:', e);
  }

  const isPressReleaseCanonical = article?.canonicalPath?.split('/')[0] === 'pressreleases';
  const isTaggedPressRelease = isPressReleaseCanonical || (await isStoryTaggedPressRelease(session, article));

  let pressReleasesByAuthor: StoryObject[] = [];

  if (isTaggedPressRelease) {
    const pressReleasesByAuthorRes = await basicNewsManager.simplyQueryNews({}, { limit: 6, type: article.type });
    if (Array.isArray(pressReleasesByAuthorRes?.ok)) {
      pressReleasesByAuthor = pressReleasesByAuthorRes.ok;

      if (Array.isArray(article.layout?.sidebar?.blocks)) {
        const target = 'acf/link-group';
        const linkGroup = deepArrayInnerBlocksSearch(article.layout.sidebar.blocks, target);
        if (linkGroup) {
          const linkGroupTitle = article?.author?.name
            ? `More Press Releases from ${capitalize(article.author.name)}`
            : 'More Press Releases';
          (linkGroup as LinkGroupBlockProps).attrs.data = {
            action: 'More Press Releases by Author Click',
            links: pressReleasesByAuthor as Link[],
            query: null as unknown as undefined,
            title: linkGroupTitle,
          };
        }
      }
    }
  }

  const tickers = getPrimaryTickers(article?.tickers);

  const { negative: negativeKeyGroups, positive: positiveKeyGroups } = getArticleKeywordsGroup(article.body);
  if (negativeKeyGroups?.length || positiveKeyGroups?.length) {
    article.dfpTags = {};

    if (negativeKeyGroups?.length) {
      article.dfpTags['BZ_NEG_KEYWORD_GROUP'] = negativeKeyGroups;
    }

    if (positiveKeyGroups?.length) {
      article.dfpTags['BZ_POS_KEYWORD_GROUP'] = positiveKeyGroups;
    }
  }

  // Move this to be fetched from the API
  const wordCount = getWordsAndCharactersCount(article.body)?.wordCount;

  const commentCountResponse = await articleManager.getCommentCount(Number(nid));
  const commentCount = commentCountResponse?.ok?.data?.count ?? 0;
  article.commentCount = commentCount;

  const robots: string | null = await canArticleBeMarkedAsNoIndex(session, article, wordCount);
  if (robots && article.metaProps) {
    article.metaProps.robots = robots;
  }

  const articleUrl = getCanonicalUrl(article);

  if (article.metaProps) {
    article.metaProps.canonical =
      !article?.canonicalPath ||
      (article?.canonicalPath &&
        ['https://www.benzinga.com/', 'https://www.benzinga.com'].includes(article?.canonicalPath))
        ? articleUrl
        : article.metaProps.canonical;

    const HTMLLanguage = translatedContentLanguages.find(language => language.storyType === article.type)?.code || 'en';
    article.metaProps.hrefLanguage = HTMLLanguage;

    article.metaProps.twitterCard = 'summary_large_image';
  }

  // DISABLING for now -- Doesn't work well with the current type of test running
  // Assuing we'll need this in a future iteration
  // const duration = DateTime.now().diff(DateTime.fromISO(article.createdAt || ''), ['days']);
  // const shouldInjectOptinMonsterPlaceholder = duration.days > 30;

  // const formattedArticleBlocksWithOptinMonster = shouldInjectOptinMonsterPlaceholder
  //   ? injectOptinMonsterInArticlePlaceholder(article.blocks as ArticleBlock[])
  //   : article.blocks;

  // article.blocks = formattedArticleBlocksWithOptinMonster;

  const questionsRequest = articleManager.getFollowUpQuestions(article?.nodeId ?? '');
  const questionsResponse = await safeTimeout(questionsRequest, 3000);
  const followUpQuestions = questionsResponse?.ok?.follow_up_questions;

  const campaignSettings = {
    disableCampaignifyUnit: shouldDisableCampaignifyUnit(article, layout?.disable_campaignify ?? false),
  };

  const pageTargeting = await getDFPTargeting(article);
  if (pageTargeting) {
    pageTargeting.newTemplate = templateFeatures.useNewTemplate;
  }

  let featuredTickers: ArticleFeaturedTicker[] = [];

  const featuredTickersRes = await tickers.map(async (ticker, index) => {
    const isCrypto = ticker?.includes('$');
    const symbol = isCrypto ? ticker?.replace('$', '') + '/USD' : ticker;
    const featuredTicker: ArticleFeaturedTicker = {
      candles: [],
      changePercent: null,
      logoUrl: null,
      name: null,
      price: null,
      symbol,
      type: 'STOCK',
    };

    const quotesManager = session.getManager(QuotesManager);
    const quote = await quotesManager.getDelayedQuotes([symbol]);

    if (quote?.ok?.[symbol] && !quote.ok[symbol].error) {
      const logoRes = await quotesManager.getQuotesLogos([symbol]);
      const markVectorLightFilesPath = lensPath([0, 'files', 'mark_vector_light']);
      const markCompositeLightFilesPath = lensPath([0, 'files', 'mark_composite_light']);
      featuredTicker.logoUrl =
        (logoRes?.ok
          ? view(markVectorLightFilesPath, logoRes.ok) || view(markCompositeLightFilesPath, logoRes.ok)
          : null) || null;
      const quoteData = quote.ok[symbol];
      featuredTicker.name = quoteData.companyStandardName ?? quoteData.description ?? null;
      featuredTicker.price = quoteData.lastTradePrice ?? null;
      featuredTicker.changePercent = quoteData.changePercent ?? null;
      featuredTicker.type = quoteData.type ?? null;

      if (quoteData.type === 'CRYPTO') {
        featuredTicker.name = featuredTicker.name.replace(' - United States Dollar', '');
      }

      let chartCandles: Candle[] = [];
      const chartManager = session.getManager(ChartManager);

      if (index === 0) {
        if (isCrypto) {
          const zone = 'America/Detroit';
          const now = DateTime.now().setZone(zone);
          const date = now.toISODate();
          const from = now.minus({ days: 1 }).toISODate();
          const to = date;
          const cryptoChartRes = await chartManager.getCryptoChart(symbol.replace('/', ''), from, to, 1, 'hour');
          const formatCryptoCandles = (item: CryptoChartResponseResult): Candle => {
            return {
              ...(item || {}),
              dateTime: item.x,
              time: item.x,
            };
          };
          chartCandles = cryptoChartRes?.ok?.results?.map(formatCryptoCandles) || [];
        } else {
          const chartRes = await chartManager.getChart({
            from: '5m',
            interval: '1d',
            symbol,
          });
          chartCandles = chartRes?.ok?.candles || [];

          const quoteAnalysis = await quotesManager.getQuoteAnalysis(symbol);
          if (quoteAnalysis?.ok) {
            featuredTicker.analysis = quoteAnalysis.ok;
          }
        }

        featuredTicker.candles = chartCandles;
      }
      return featuredTicker;
    }
    return null;
  });

  const resolvedTickers = await Promise.all(featuredTickersRes);
  featuredTickers = resolvedTickers.filter((ticker): ticker is ArticleFeaturedTicker => ticker !== null);

  let rankingData: RankingDetail | null = null;
  if (featuredTickers[0]?.symbol) {
    const quotesManager = session.getManager(QuotesManager);
    const tickerDetailsResponse = await quotesManager.getTickerDetails([featuredTickers[0]?.symbol]);
    rankingData = tickerDetailsResponse?.ok?.result?.[0]?.rankings ?? null;
  }

  return {
    props: {
      article,
      baseUrl: baseUrl ?? 'https://www.benzinga.com',
      campaignSettings,
      campaigns,
      deviceType: deviceInfo.deviceType ?? null,
      disableRaptiveReadyOnPageLoad: true,
      featuredTickers,
      followUpQuestions: followUpQuestions ?? null,
      headerProps,
      isTaggedPressRelease,
      layout,
      metaProps: article?.metaProps ?? null,
      nid: nid,
      pageTargeting,
      pressReleasesByAuthor,
      rankingData,
      shouldRenderBottomTaboola: isBzApp ? templateFeatures.hasTaboola : false,
      tickers,
      useNewTemplate: isBzApp ? templateFeatures.useNewTemplate : false,
      wordCount,
    },
    statusCode: 200,
  };
};
