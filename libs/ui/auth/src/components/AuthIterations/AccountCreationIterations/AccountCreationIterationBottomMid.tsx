import { FC, useState, useEffect, useContext } from 'react';
import { BzImage } from '@benzinga/image';
import Link from 'next/link';
import { FaCheck } from 'react-icons/fa';
import { useTranslation } from 'react-i18next';
import { AuthIterationType } from '../types';
import SocialButton from '../SocialButton';
import { TrackingManager } from '@benzinga/tracking-manager';
import { SessionContext } from '@benzinga/session-context';

const utmSource = 'account-creation-bottom-mid';

export const AccountCreationIterationBottomMid: FC<AuthIterationType> = ({ nextUrl, onSocialClicked, placement }) => {
  const session = useContext(SessionContext);
  const { t } = useTranslation(['auth', 'common']);
  const [selectedOption, setSelectedOption] = useState(0);
  const [checkoutUrl, setCheckoutUrl] = useState<string | undefined>(
    `https://www.benzinga.com/premium/ideas/benzinga-edge-checkout/?utm_source=${utmSource}&t=be8be9we6ja3paso1`,
  );
  const registerUrl = `/login?utm_source=${utmSource}&is_paywalled=true&t=be8be9we6ja3paso1&action=register${nextUrl ? `&next=${nextUrl}` : ''}`;
  const loginUrl = `/login?utm_source=${utmSource}&is_paywalled=true&t=be8be9we6ja3paso1&action=login${nextUrl ? `&next=${nextUrl}` : ''}`;

  useEffect(() => {
    session.getManager(TrackingManager).trackPaywallEvent('view', {
      paywall_id: utmSource,
      paywall_type: 'soft',
      placement: placement,
    });
  }, [session, placement]);

  return (
    <>
      <div className="fixed inset-0 backdrop-blur-xl z-[1000002]"></div>
      <div className="w-full h-full md:h-auto border border-black animate-fade-up animate-delay-300 animate-once no-scrollbar overflow-scroll flex flex-col md:flex-row items-start md:gap-12 fixed z-[1000003] bg-blue-950 bottom-0 left-0 p-6 md:p-12 shadow-xl">
        <div className="text-white">
          <Link
            href={`https://www.benzinga.com/premium/ideas/benzinga-edge-checkout/?utm_source=${utmSource}&t=be8be9we6ja3paso1`}
            target="_blank"
          >
            <BzImage height="25px" src="/next-assets/images/bz-edge-logo.svg" width="240px" />
          </Link>
          <h3 className="text-4xl text-white mb-4">Get Unlimited Access</h3>
          <p className="text-white">
            Come see why nearly <span className="font-black">10,000 INVESTORS</span> joined us{' '}
            <span className="font-black">THIS WEEK ALONE</span>!
          </p>
          <p className="max-w-[400px]">
            <Link className="underline" href="/reports/TSLA" rel="noopener noreferrer" target="_blank">
              In Depth Reports
            </Link>
            ,{' '}
            <Link className="underline" href="/analyst-stock-ratings" rel="noopener noreferrer" target="_blank">
              Analyst Ratings
            </Link>
            ,{' '}
            <Link className="underline" href="/earnings" rel="noopener noreferrer" target="_blank">
              Earnings
            </Link>
            , Trade Ideas, and more! Or return to{' '}
            <Link className="underline" href="/" rel="noopener noreferrer" target="_blank">
              Benzinga.com
            </Link>
          </p>
        </div>
        <div className="w-[1px] bg-blue-400 hidden md:block"></div>
        <div className="flex gap-4 flex-col-reverse xl:flex-row xl:gap-12">
          <div className="max-w-[500px]">
            <div className="flex gap-2">
              <SocialButton nextUrl={checkoutUrl} onSocialClicked={onSocialClicked} socialType="google" />
              <SocialButton nextUrl={checkoutUrl} onSocialClicked={onSocialClicked} socialType="apple" />
              <SocialButton nextUrl={checkoutUrl} onSocialClicked={onSocialClicked} socialType="microsoft" />
            </div>
            <div className="flex-1 text-center my-4 relative">
              <div className="w-full h-[1px] bg-blue-400 absolute top-1/2 left-0"></div>
              <span className="text-blue-400 relative bg-blue-950 px-4">{t('Auth.AuthModal.continue-row')}</span>
            </div>

            <div className="flex gap-4 mb-8">
              <Link
                className="flex-1 p-2 bg-blue-700 text-center text-white hover:bg-blue-600 font-bold lg:p-3"
                href={loginUrl}
                target={checkoutUrl ? '_blank' : undefined}
              >
                LOGIN
              </Link>
              <Link
                className="flex-1 p-2 bg-blue-700 text-center text-white hover:bg-blue-600 font-bold lg:p-3"
                href={checkoutUrl ?? registerUrl}
                target={checkoutUrl ? '_blank' : undefined}
              >
                SIGN UP
              </Link>
            </div>
          </div>
          <div className="w-[1px] bg-blue-400"></div>
          <div className="text-white flex flex-col gap-4 justify-center">
            <div
              className={`shadow-lg rounded-lg p-4 flex items-center border-4 border-blue-500 ${selectedOption === 0 ? 'bg-white text-black' : 'bg-transparent text-white'}`}
              onClick={() => {
                setSelectedOption(0);
                setCheckoutUrl(
                  `https://www.benzinga.com/premium/ideas/benzinga-edge-checkout/?utm_source=${utmSource}&t=be8be9we6ja3paso1`,
                );
                window.open(
                  `https://www.benzinga.com/premium/ideas/benzinga-edge-checkout/?utm_source=${utmSource}&t=be8be9we6ja3paso1`,
                  '_blank',
                );
              }}
            >
              <div
                className={`hidden md:flex size-8 min-w-8 min-h-8 border-2 shadow-xl rounded-md ml-2 mr-6 justify-center items-center ${selectedOption === 0 ? 'border-black' : 'border-white'}`}
              >
                {selectedOption === 0 ? <FaCheck /> : undefined}
              </div>
              <div>
                <div className="text-md font-bold">Benzinga Edge - $79 Annually (Limited Time Offer)</div>
                <p className="text-xs max-w-[500px] !mb-0">
                  Unlock more trading potential! Get access to our premium features and tools to help you trade better.
                </p>
                <Link
                  className="text-xs font-bold my-0"
                  href={`https://www.benzinga.com/premium/ideas/benzinga-edge-checkout/?utm_source=${utmSource}&t=be8be9we6ja3paso1`}
                  target="_blank"
                >
                  (Learn More)
                </Link>
              </div>
            </div>
            <div
              className={`shadow-lg rounded-lg p-4 flex items-center border-4 border-blue-500 ${selectedOption === 1 ? 'bg-white text-black' : 'bg-transparent text-white'}`}
              onClick={() => {
                setSelectedOption(1);
                setCheckoutUrl(undefined);
              }}
            >
              <div
                className={`hidden md:flex size-8 min-w-8 min-h-8 border-2 shadow-xl rounded-md ml-2 mr-6 justify-center items-center ${selectedOption === 1 ? 'border-black' : 'border-white'}`}
              >
                {selectedOption === 1 ? <FaCheck /> : undefined}
              </div>
              <div>
                <div className="text-md font-bold">Free Access - FREE</div>
                <p className="text-xs max-w-[500px]">Get limited access to Analyst Ratings, Earnings, and more!</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </>
  );
};
