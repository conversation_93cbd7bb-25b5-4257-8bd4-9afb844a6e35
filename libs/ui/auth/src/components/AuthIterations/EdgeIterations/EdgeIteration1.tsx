import { FC, useContext, useEffect, useState } from 'react';
import { useHydrate } from '@benzinga/hooks';
import { useIsUserLoggedIn, usePermission } from '@benzinga/user-context';
import { BzImage } from '@benzinga/image';
import { Authentication, AuthMode } from '@benzinga/session';
import { SafeType } from '@benzinga/safe-await';
import { Login } from '../../Login';
import { Register } from '../../Register';
import Link from 'next/link';
import { FaCheck } from 'react-icons/fa';
import { FiX } from 'react-icons/fi';
import { useTranslation } from 'react-i18next';
import { AuthIterationType } from '../types';
import { TrackingManager } from '@benzinga/tracking-manager';
import { SessionContext } from '@benzinga/session-context';
import { edgePaywallCopy, getEdgePaywallUtmSource } from '../utils';

export const EdgeIteration1: FC<AuthIterationType> = ({
  allowClose,
  contentType,
  nextUrl,
  onLogin,
  onRegister,
  onSocialClicked,
  phoneNumber: phone,
  placement,
  setShowPaywall,
}) => {
  const session = useContext(SessionContext);
  const { t } = useTranslation('common');
  const isUserLoggedIn = useHydrate(useIsUserLoggedIn(), false);
  const hasAccess = usePermission('com/reports', '#');
  const [authMode, setAuthMode] = useState<AuthMode>('register');
  const [selectedOption, setSelectedOption] = useState(0);
  const paywallCopy = contentType ? edgePaywallCopy[contentType] : null;
  const headline = paywallCopy ? paywallCopy.utm : 'get-unlimited-access-with-login-register';
  const utmSource = getEdgePaywallUtmSource({ edgeIteration: 1, headline, type: allowClose ? 'soft' : 'hard' });

  const [phoneNumber, setPhoneNumber] = useState(phone);
  const [authentication, setAuthentication] = useState<SafeType<Authentication> | null>(null);
  const [checkoutUrl, setCheckoutUrl] = useState<string | undefined>(
    `https://www.benzinga.com/premium/ideas/benzinga-edge-checkout/?utm_source=${utmSource}&t=be8be9we6ja3paha2`,
  );
  const registerUrl = `/login?utm_source=${utmSource}&is_paywalled=true&t=be8be9we6ja3paha2&action=register${nextUrl ? `&next=${nextUrl}` : ''}`;

  useEffect(() => {
    session.getManager(TrackingManager).trackPaywallEvent('view', {
      paywall_id: utmSource,
      paywall_type: allowClose ? 'soft' : 'hard',
      placement: placement,
    });
  }, [allowClose, session, placement, utmSource]);

  return (
    <>
      <div className="fixed inset-0 z-[1000002] touch-none pointer-events-none"></div>
      <div className="paywall-content w-full max-h-[100vh] rounded-t-2xl text-white md:h-auto border border-black animate-fade-up animate-delay-300 animate-once no-scrollbar overflow-scroll flex flex-col md:flex-row items-center md:items-start justify-start md:justify-center md:gap-12 fixed z-[1000003] bg-blue-950 bottom-0 left-0 p-6 shadow-xl">
        {allowClose && setShowPaywall && (
          <button
            className="absolute top-6 right-6 text-white"
            onClick={() => {
              setShowPaywall(false);
            }}
          >
            <FiX />
          </button>
        )}
        <div className="text-white flex flex-col gap-8 mt-4">
          <div className="text-white">
            <Link
              href={`https://www.benzinga.com/premium/ideas/benzinga-edge-checkout/?utm_source=${utmSource}&t=be8be9we6ja3paha2`}
              target="_blank"
            >
              <BzImage height="18px" src="/next-assets/images/bz-edge-logo.svg" width="180px" />
            </Link>
            {paywallCopy ? (
              <>
                <h2 className="text-4xl text-white my-4">{paywallCopy.headline}</h2>
                <p className="text-white">{paywallCopy.subhead}</p>
              </>
            ) : (
              <>
                <h3 className="text-4xl text-white my-4">Get Unlimited Access</h3>
                <p className="text-white">
                  Come see why over <span className="font-black">10,000 INVESTORS</span> joined us{' '}
                  <span className="font-black">THIS WEEK ALONE</span>!
                </p>
                <p className="max-w-[400px]">
                  <Link className="underline" href="/reports/TSLA" rel="noopener noreferrer" target="_blank">
                    In Depth Reports
                  </Link>
                  ,{' '}
                  <Link className="underline" href="/analyst-stock-ratings" rel="noopener noreferrer" target="_blank">
                    Analyst Ratings
                  </Link>
                  ,{' '}
                  <Link className="underline" href="/earnings" rel="noopener noreferrer" target="_blank">
                    Earnings
                  </Link>
                  , Trade Ideas, and more! Or return to{' '}
                  <Link className="underline" href="/" rel="noopener noreferrer" target="_blank">
                    Benzinga.com
                  </Link>
                </p>
              </>
            )}
          </div>
          <div
            className={`shadow-lg rounded-lg p-4 flex items-center border-4 border-blue-500 ${selectedOption === 0 ? 'bg-white text-black' : 'bg-transparent text-white'}`}
            onClick={() => {
              setSelectedOption(0);
              setCheckoutUrl(
                `https://www.benzinga.com/premium/ideas/benzinga-edge-checkout/?utm_source=${utmSource}&t=be8be9we6ja3paha2`,
              );
              window.open(
                `https://www.benzinga.com/premium/ideas/benzinga-edge-checkout/?utm_source=${utmSource}&t=be8be9we6ja3paha2`,
                '_blank',
              );
            }}
          >
            <div
              className={`hidden md:flex size-8 min-w-8 min-h-8 border-2 shadow-xl rounded-md ml-2 mr-6 justify-center items-center ${selectedOption === 0 ? 'border-black' : 'border-white'}`}
            >
              {selectedOption === 0 ? <FaCheck /> : undefined}
            </div>
            <div>
              <div className="text-md font-bold">Benzinga Edge - $79 Annually (Limited Time Offer)</div>
              <p className="text-xs max-w-[500px] !mb-0">
                Unlock more trading potential! Get access to our premium features and tools to help you trade better.
              </p>
              <Link
                className="text-xs font-bold my-0"
                href={`https://www.benzinga.com/premium/ideas/benzinga-edge-checkout/?utm_source=${utmSource}&t=be8be9we6ja3paha2`}
                target="_blank"
              >
                (Learn More)
              </Link>
            </div>
          </div>
          <Link
            className={`flex-1 p-2 bg-blue-700 text-center text-white hover:bg-blue-600 rounded-md border border-white font-bold lg:p-4 ${!hasAccess && isUserLoggedIn ? 'block' : 'block xl:hidden'}`}
            href={checkoutUrl ?? registerUrl}
            target={checkoutUrl ? '_blank' : undefined}
          >
            {!paywallCopy
              ? 'SUBSCRIBE'
              : Array.isArray(paywallCopy.button)
                ? paywallCopy.button[Math.floor(Math.random() * paywallCopy.button.length)]
                : paywallCopy.button}
          </Link>
        </div>
        {!isUserLoggedIn && (authMode === 'login' || authMode === 'register') && (
          <div className="max-w-[500px] hidden xl:block">
            {authMode === 'login' && (
              <div className="select-none">
                <Login onLogin={onLogin} onSocialClicked={onSocialClicked} setAuthMode={setAuthMode} />
              </div>
            )}
            {authMode === 'register' && (
              <div className="select-none">
                <Register
                  onRegister={onRegister}
                  onSocialClicked={onSocialClicked}
                  setAuthentication={setAuthentication}
                  setAuthMode={setAuthMode}
                  setPhoneNumber={setPhoneNumber}
                />
              </div>
            )}
          </div>
        )}
      </div>
    </>
  );
};
