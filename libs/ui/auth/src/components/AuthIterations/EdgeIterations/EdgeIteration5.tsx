import { FC, useContext, useEffect, useState } from 'react';
import { useHydrate } from '@benzinga/hooks';
import { useIsUserLoggedIn, usePermission } from '@benzinga/user-context';
import { BzImage } from '@benzinga/image';
import Link from 'next/link';
import { FaCheck } from 'react-icons/fa';
import { useTranslation } from 'react-i18next';
import { AuthIterationType } from '../types';
import SocialButton from '../SocialButton';
import { FiX } from 'react-icons/fi';
import { TrackingManager } from '@benzinga/tracking-manager';
import { SessionContext } from '@benzinga/session-context';
import { edgePaywallCopy, getEdgePaywallUtmSource } from '../utils';

export const EdgeIteration5: FC<AuthIterationType> = ({
  allowClose,
  contentType,
  nextUrl,
  onSocialClicked,
  placement,
  setShowPaywall,
}) => {
  const session = useContext(SessionContext);
  const { t } = useTranslation('common');
  const isUserLoggedIn = useHydrate(useIsUserLoggedIn(), false);
  const hasAccess = usePermission('com/reports', '#');
  const [selectedOption, setSelectedOption] = useState(0);
  const paywallCopy = contentType ? edgePaywallCopy[contentType] : null;
  const headline = paywallCopy ? paywallCopy.utm : 'edge-unlimited-access';
  const utmSource = getEdgePaywallUtmSource({ edgeIteration: 5, headline, type: allowClose ? 'soft' : 'hard' });

  const [checkoutUrl, setCheckoutUrl] = useState<string | undefined>(
    `https://www.benzinga.com/premium/ideas/benzinga-edge-checkout/?utm_source=${utmSource}&t=be8be9we6ja3paha2`,
  );
  const registerUrl = `/login?utm_source=${utmSource}&is_paywalled=true&t=be8be9we6ja3paha2&action=register${nextUrl ? `&next=${nextUrl}` : ''}`;
  const loginUrl = `/login?utm_source=${utmSource}&is_paywalled=true&t=be8be9we6ja3paha2&action=login${nextUrl ? `&next=${nextUrl}` : ''}`;

  useEffect(() => {
    session.getManager(TrackingManager).trackPaywallEvent('view', {
      paywall_id: utmSource,
      paywall_type: allowClose ? 'soft' : 'hard',
      placement: placement,
    });
  }, [allowClose, session, placement, utmSource]);

  return (
    <>
      <div className="fixed inset-0 z-[1000002] touch-none pointer-events-none"></div>
      <div className="paywall-content w-full h-full md:h-auto border border-black animate-fade-up animate-delay-300 animate-once no-scrollbar overflow-scroll flex flex-col md:flex-row items-start md:gap-12 fixed z-[1000003] bg-blue-950 bottom-0 left-0 p-6 shadow-xl">
        {allowClose && setShowPaywall && (
          <button
            className="absolute top-6 right-6 text-white"
            onClick={() => {
              setShowPaywall(false);
            }}
          >
            <FiX />
          </button>
        )}
        <div className="text-white">
          <Link
            href={`https://www.benzinga.com/premium/ideas/benzinga-edge-checkout/?utm_source=${utmSource}&t=be8be9we6ja3paha2`}
            target="_blank"
          >
            <BzImage height="18px" src="/next-assets/images/bz-edge-logo.svg" width="180px" />
          </Link>
          {paywallCopy ? (
            <>
              <h2 className="text-4xl text-white my-4">{paywallCopy.headline}</h2>
              <p className="text-white">{paywallCopy.subhead}</p>
            </>
          ) : (
            <>
              <h3 className="text-4xl text-white my-4">Get Unlimited Access</h3>
              <p className="text-white">
                Come see why nearly <span className="font-black">10,000 INVESTORS</span> joined us{' '}
                <span className="font-black">THIS WEEK ALONE</span>!
              </p>
              <p className="max-w-[400px]">
                <Link className="underline" href="/reports/TSLA" rel="noopener noreferrer" target="_blank">
                  In Depth Reports
                </Link>
                ,{' '}
                <Link className="underline" href="/analyst-stock-ratings" rel="noopener noreferrer" target="_blank">
                  Analyst Ratings
                </Link>
                ,{' '}
                <Link className="underline" href="/earnings" rel="noopener noreferrer" target="_blank">
                  Earnings
                </Link>
                , Trade Ideas, and more! Or return to{' '}
                <Link className="underline" href="/" rel="noopener noreferrer" target="_blank">
                  Benzinga.com
                </Link>
              </p>
            </>
          )}
        </div>
        <div className="w-[1px] bg-blue-400 hidden md:block"></div>
        <div className="flex gap-4 flex-col-reverse xl:flex-row xl:gap-12">
          {!isUserLoggedIn && (
            <div className="max-w-[500px]">
              <div className="flex gap-2">
                <SocialButton nextUrl={checkoutUrl} onSocialClicked={onSocialClicked} socialType="google" />
                <SocialButton nextUrl={checkoutUrl} onSocialClicked={onSocialClicked} socialType="apple" />
                <SocialButton nextUrl={checkoutUrl} onSocialClicked={onSocialClicked} socialType="microsoft" />
              </div>
              <div className="flex-1 text-center my-4 relative">
                <div className="w-full h-[1px] bg-blue-400 absolute top-1/2 left-0"></div>
                <span className="text-blue-400 relative bg-blue-950 px-4">
                  {t('Auth.AuthModal.continue-row', { ns: 'auth' })}
                </span>
              </div>

              <div className="flex gap-4 mb-8">
                <Link
                  className="flex-1 p-2 bg-blue-700 text-center text-white hover:bg-blue-600 font-bold lg:p-3"
                  href={loginUrl}
                  target={checkoutUrl ? '_blank' : undefined}
                >
                  LOGIN
                </Link>
                <Link
                  className="flex-1 p-2 bg-blue-700 text-center text-white hover:bg-blue-600 font-bold lg:p-3"
                  href={checkoutUrl ?? registerUrl}
                  target={checkoutUrl ? '_blank' : undefined}
                >
                  SIGN UP
                </Link>
              </div>
            </div>
          )}
          <div className="w-[1px] bg-blue-400"></div>
          <div className="text-white flex flex-col gap-4 justify-center">
            <div
              className={`min-w-[300px] shadow-lg rounded-lg p-4 flex items-center border-4 border-blue-500 ${selectedOption === 0 ? 'bg-white text-black' : 'bg-transparent text-white'}`}
              onClick={() => {
                setSelectedOption(0);
                setCheckoutUrl(
                  `https://www.benzinga.com/premium/ideas/benzinga-edge-checkout/?utm_source=${utmSource}&t=be8be9we6ja3paha2`,
                );
                window.open(
                  `https://www.benzinga.com/premium/ideas/benzinga-edge-checkout/?utm_source=${utmSource}&t=be8be9we6ja3paha2`,
                  '_blank',
                );
              }}
            >
              <div
                className={`hidden md:flex size-8 min-w-8 min-h-8 border-2 shadow-xl rounded-md ml-2 mr-6 justify-center items-center ${selectedOption === 0 ? 'border-black' : 'border-white'}`}
              >
                {selectedOption === 0 ? <FaCheck /> : undefined}
              </div>
              <div>
                <div className="text-md font-bold">Benzinga Edge - $79 Annually (Limited Time Offer)</div>
                <p className="text-xs max-w-[500px] !mb-0">
                  Unlock more trading potential! Get access to our premium features and tools to help you trade better.
                </p>
                <Link
                  className="text-xs font-bold my-0"
                  href={`https://www.benzinga.com/premium/ideas/benzinga-edge-checkout/?utm_source=${utmSource}&t=be8be9we6ja3paha2`}
                  target="_blank"
                >
                  (Learn More)
                </Link>
              </div>
            </div>
            {!hasAccess && isUserLoggedIn && (
              <Link
                className="flex-1 p-2 bg-blue-700 text-center text-white hover:bg-blue-600 rounded-md border border-white font-bold lg:p-4"
                href={checkoutUrl ?? registerUrl}
                target={checkoutUrl ? '_blank' : undefined}
              >
                {!paywallCopy
                  ? 'SUBSCRIBE'
                  : Array.isArray(paywallCopy.button)
                    ? paywallCopy.button[Math.floor(Math.random() * paywallCopy.button.length)]
                    : paywallCopy.button}
              </Link>
            )}
          </div>
        </div>
      </div>
    </>
  );
};
