import { useState } from 'react';
import { <PERSON><PERSON><PERSON><PERSON> } from '@benzinga/logos-ui';
import axios from 'axios';

export interface ReportSignUpProps {
  authEmail?: string;
  onClose?: () => void;
}

const offers = [
  // {
  //   description:
  //     'Our brand-new free report reveals three little-known stocks with massive insider buying. Get immediate access to the tickers and expert analysis of these stocks.',
  //   emailPrompt: 'Enter your email below to receive the report',
  //   headline: 'Buy These 3 Tech Stocks Insiders Love',
  //   utm: 'tech_stocks_insiders',
  // },
  // {
  //   description:
  //     "Beat inflation with these two low-priced, high-yield stocks that are must-adds to every income investor's portfolio. Both stocks (and a bonus fund) are under $10 and yield over 10%!",
  //   emailPrompt: 'Enter your email below to get instant access',
  //   headline: 'Buy These 2 High-Yield Dividend Stocks Under $10',
  //   utm: 'two_dividends_under_10',
  // },
  // {
  //   description:
  //     "We've uncovered 3 under-the-radar income stocks poised to turn rate cuts into a potential cash windfall. Get the tickers and our expert analysis today.",
  //   emailPrompt: 'Enter your email to claim your free dividend report',
  //   headline: 'Buy These 3 Stocks Growing Dividends Even as Rates Decline',
  //   utm: 'growing_dividends_rates_decline',
  // },
  {
    description:
      'Discover how to profit BIG with the specific stocks and sectors our experts predict will outperform the market the rest of this year.',
    emailPrompt: 'Enter your email below to receive the report',
    headline: 'TOP 7 STOCKS TO BUY FOR THE RESTS OF 2025',
    utm: 'register-upsell-top-7-stocks',
  },
];

export const ReportSignUp = ({ authEmail, onClose }: ReportSignUpProps) => {
  const [offerIndex, setOfferIndex] = useState<null | number>(0);
  const [email, setEmail] = useState(authEmail ?? '');
  const [showSuccess, setShowSuccess] = useState(false);
  const [error, setError] = useState(false);

  // useEffect(() => {
  //   const index = Math.floor(Math.random() * offers.length);
  //   setOfferIndex(index);
  // }, []);

  const subscribe = async () => {
    setError(false);
    if (!email || offerIndex === null) return;

    const res = await axios.post('/api/iterable-workflow', {
      dataFields: {
        utm_campaign: 'top_7_stocks_site_reg',
      },
      email,
      workflowId: 577472,
    });

    if (res.status !== 200) {
      setError(true);
    } else {
      setShowSuccess(true);
      setTimeout(() => {
        onClose && onClose();
      }, 3000);
    }
  };

  const handleContinue = () => {
    onClose && onClose();
  };

  if (offerIndex !== null) {
    const { description, emailPrompt, headline } = offers[offerIndex];
    return (
      <div className="p-6 md:p-8 md:pb-8 bg-bzblue-800/90 bg-[url('/next-assets/images/services/services-hero.png')] bg-cover bg-no-repeat bg-bottom">
        <div className="mb-4">
          <div className="mb-4 w-full max-w-[240px]">
            <BenzingaLogo height={14} />
          </div>
          <h1 className="text-white capitalize text-3xl">{headline}</h1>
        </div>
        <p>{description}</p>
        <div className="border border-bzblue-600 bg-bzblue-600/40 p-4 rounded-md text-center my-8 flex flex-col">
          <label className="pb-2" htmlFor="email">
            {emailPrompt}
          </label>
          <div className="flex flex-row">
            <input
              className="w-full text-bzblue-1000 px-2"
              name="email"
              onChange={e => setEmail(e.target.value)}
              type="email"
              value={email}
            />
            <button
              className="bg-bzblue-900 text-white px-4 md:px-8 py-2 rounded-sm whitespace-nowrap"
              onClick={subscribe}
            >
              Sign up now
            </button>
          </div>
          {showSuccess && <p className="text-white text-sm mt-2">Thank you for signing up! Closing in 3..2..1</p>}
          {error && (
            <p className="text-red-200 text-sm mt-2">Error with signing up, please contact our support team.</p>
          )}
        </div>
        <div className="flex justify-center">
          <button className="capitalize hover:underline font-semibold" onClick={handleContinue}>
            No Thanks
          </button>
        </div>
      </div>
    );
  } else {
    return null;
  }
};
