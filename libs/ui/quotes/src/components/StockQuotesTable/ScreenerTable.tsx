'use client';
import React, { useCallback, useEffect } from 'react';
import styled, { useTheme } from '@benzinga/themetron';
import { DelayedQuote, QuotesManager } from '@benzinga/quotes-manager';
import { SessionContext } from '@benzinga/session-context';
import { getColorByValue } from '@benzinga/frontend-utils';
import { formatLarge } from '@benzinga/utils';
import { Button } from '@benzinga/core-ui';
import { CalendarManager } from '@benzinga/calendar-manager';
import { DateTime } from 'luxon';
import { getDetailedData } from '@benzinga/calendar-manager';
import { ScreenerTableEdgeCTA } from './ScreenerTableEdgeCTA';
import { TrackingManager } from '@benzinga/tracking-manager';
import { usePermission } from '@benzinga/user-context';
import Link from 'next/link';

export interface ScreenerTableProps {
  bypassPaywall?: boolean;
  quotes?: DelayedQuote[];
}

const SEARCH_ITEM_LOGO_SIZE = 40;
const IMAGE_REQUEST_PARAMS = {
  fields: 'mark_vector_light,logo_vector_light',
  scale: `${SEARCH_ITEM_LOGO_SIZE}x${SEARCH_ITEM_LOGO_SIZE}`,
  search_keys_type: 'symbol',
};
export const ScreenerTable: React.FC<ScreenerTableProps> = ({ bypassPaywall = false, quotes }) => {
  const [tickerLogos, setTickerLogos] = React.useState({});
  const [sumamaryRatings, setSumamaryRatings] = React.useState({});
  const [showAllData, setShowAllData] = React.useState<boolean>(false);
  const theme = useTheme();
  const session = React.useContext(SessionContext);
  const quotesManager = session.getManager(QuotesManager);
  const calendarManager = session.getManager(CalendarManager);
  const hasPermission = usePermission('com/read', 'unlimited-calendars');

  const getQuoteLogoAndRatings = useCallback(
    async quotes => {
      const symbols = quotes ? quotes.map(item => item.symbol) : [];
      if (symbols.length > 0) {
        const { ok: logos } = await quotesManager.getQuotesLogosMapped(symbols, IMAGE_REQUEST_PARAMS);
        if (logos) {
          setTickerLogos(logos);
        }

        if (showAllData) {
          const dateFrom = DateTime.fromJSDate(new Date()).minus({ years: 3 }).toJSDate();
          const params = {
            dateFrom,
            symbols: symbols,
          };

          const sumamaryRatings = [];
          calendarManager.getCalendarData('ratings', params, true).then(res => {
            if (res?.ok) {
              const grouped = groupBy(res?.ok, 'ticker');

              Object.keys(grouped).map(symbol => {
                sumamaryRatings[symbol] = getDetailedData(grouped[symbol]);
              });
            }
            setSumamaryRatings(sumamaryRatings);
          });
        }
      }
    },
    [calendarManager, quotesManager, showAllData],
  );

  useEffect(() => {
    if (hasPermission || bypassPaywall) {
      setShowAllData(true);
    } else {
      session.getManager(TrackingManager).trackPaywallEvent('view', {
        paywall_id: 'edge-screener-table-benzinga-edge',
        paywall_type: 'hard',
        placement: 'screener-table',
      });
    }
    getQuoteLogoAndRatings(quotes);
  }, [getQuoteLogoAndRatings, hasPermission, session, quotes, bypassPaywall]);

  const groupBy = (xs: any[], key: string) => {
    return xs.reduce((rv, x) => {
      (rv[x[key]] = rv[x[key]] || []).push(x);
      return rv;
    }, {});
  };

  // useEffect(() => {
  //   getQuoteLogoAndRatings(quotes);
  //   // eslint-disable-next-line react-hooks/exhaustive-deps
  // }, [quotes]);

  return (
    <>
      <Container className="stock-quotes-table-container font-manrope">
        <table className="table-auto">
          <thead>
            <tr>
              <th colSpan={1}>Name/Industry</th>
              <th colSpan={1}>Price/Change</th>
              <th colSpan={1}>Dividend Yield</th>
              <th colSpan={1}>Annual Dividend</th>
              <th colSpan={1}>Market Cap</th>
              <th colSpan={1}>P/E Ratio</th>
              <th colSpan={1}>AVG Analyst Opinion</th>
              <th colSpan={1}>Mean Price Target</th>
              <th colSpan={1}></th>
            </tr>
          </thead>
          <tbody>
            {quotes &&
              quotes.map((quote, i) =>
                quote ? (
                  <tr key={`quote-${i}`}>
                    <td className="ticker-data">
                      <div className="flex info-wrap gap-3">
                        {tickerLogos[quote.symbol] && (
                          <img alt={`${quote.symbol} logo`} src={tickerLogos[quote.symbol].files?.logo_vector_light} />
                        )}
                        <div className="company-info flex flex-col">
                          <div className="flex items-center gap-2">
                            <Link href={`/quote/${quote.symbol}`}>
                              <span className="symbol font-semibold">{quote.symbol}</span>{' '}
                              <span className="company-name">{quote.companyStandardName}</span>
                            </Link>
                          </div>
                          <div className="industry">{quote.industry}</div>
                        </div>
                      </div>
                    </td>
                    <td className="price font-semibold">
                      ${quote?.lastTradePrice}
                      <div className="flex gap-1">
                        <span
                          className="change"
                          style={{ color: getColorByValue(theme, quote.change) }}
                          title={quote.change?.toString()}
                        >
                          ${quote.change ?? '– '}
                        </span>
                        <span
                          className="change-percent"
                          style={{
                            backgroundColor: `${getColorByValue(theme, quote.changePercent)}20`,
                            color: getColorByValue(theme, quote.changePercent),
                          }}
                          title={quote.changePercent?.toString()}
                        >
                          {quote.changePercent ?? '– '}%
                        </span>
                      </div>
                    </td>
                    <td className="font-semibold">{quote.dividendYield ?? '-'}%</td>
                    <td className="font-semibold">{quote.dividend ? `$${quote.dividend}` : '-'}</td>
                    <td className="font-semibold">
                      {quote?.marketCap ? `$${formatLarge(Number(quote?.marketCap))}` : '-'}
                    </td>
                    <td className="font-semibold">{quote?.pe ? quote?.pe.toFixed(2) : '-'}</td>
                    {!showAllData ? (
                      <>{i == 0 && <ScreenerTableEdgeCTA colSpan={2} rowSpan={quotes.length} />}</>
                    ) : (
                      <>
                        <td>{sumamaryRatings[quote.symbol]?.consensusRating ?? '-'}</td>
                        <td className="font-semibold">{sumamaryRatings[quote.symbol]?.consensusPriceTarget ?? '-'}</td>
                      </>
                    )}
                    <td className="button-wrap">
                      <div className="flex gap-2">
                        <Button
                          as="a"
                          href={`https://www.benzinga.com/reports/${quote.symbol}`}
                          target="_blank"
                          variant="flat-light-blue"
                        >
                          Get Report
                        </Button>
                        <Button
                          as="a"
                          href={`https://www.benzinga.com/go/start-investing/?pl=buy&utm_source=/quote/${quote.symbol}`}
                          target="_blank"
                          variant="flat-light-blue"
                        >
                          Buy
                        </Button>
                      </div>
                    </td>
                  </tr>
                ) : null,
              )}
          </tbody>
        </table>
      </Container>
      <div className="bz-pro-action">
        <a href="https://www.benzinga.com/go/benzinga-pro" rel="noreferrer" target="_blank">
          Start Your Free 14-Day Benzinga Pro Trial
        </a>
      </div>
    </>
  );
};

const Container = styled.div`
  &.stock-quotes-table-container {
    width: 100%;
    display: block;
    overflow-x: auto;
    clear: both;
    table {
      width: 100%;
      margin-bottom: 0.2rem;
      table-layout: auto;
      display: table;
      height: 100%;
    }

    thead {
      border-bottom: 1px solid #e1ebfa;

      tr {
        text-align: left;
        th {
          padding: 4px 8px;
          &:first-of-type {
            min-width: 260px;
          }
          &:nth-child(2) {
            min-width: 140px;
          }
          &:last-of-type {
            min-width: 180px;
          }
        }
      }
    }

    tbody {
      overflow: auto;

      tr {
        border-collapse: collapse;
        box-sizing: inherit;
        border-bottom: 1px solid #e1ebfa;
        width: 100%;

        td {
          padding: 6px;
          color: ${({ theme }) => theme.colorPalette.black};
          &.ticker-data {
            img {
              width: 35px;
            }
            .company-info {
              a {
                color: #000000;
              }
              .company-name {
                padding: 2px 4px;
                border-radius: 4px;
                background: #e1ebfa;
                font-size: 12px;
                line-height: 16px;
              }
              .industry {
                font-size: 0.8rem;
                color: #395173;
              }
            }
          }
          &.price {
            .change-percent {
              padding: 0px 4px;
              border-radius: 4px;
            }
          }
          &.button-wrap {
            width: 120px;
          }
        }
      }
    }
  }
`;
