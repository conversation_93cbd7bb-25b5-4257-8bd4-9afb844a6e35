'use client';
import { FC } from 'react';
import dynamic from 'next/dynamic';
import styled from '@benzinga/themetron';
import { Table, ColumnDef } from '@benzinga/table';
import { CalendarDataI } from '../../lib/interfaces';
import { convertAgGridColumnDefsToTableColumnDefs } from '../utils';

const BasicAdBanner = dynamic(() => import('@benzinga/ads').then(module => module.BasicAdBanner), {
  ssr: true,
});

interface ServerSideCalendarProps {
  calendar?: string;
  calendarData: CalendarDataI;
  calendarDataSet: any[];
  hasStockTableStyling?: boolean;
  height?: number;
  hiddenColumns?: string[];
  hiddenColumnsWhenGated?: Set<string>;
  isGated?: boolean;
  showAd?: boolean;
  ticker?: string;
  noDataText?: string;
}

export const ServerSideCalendar: FC<ServerSideCalendarProps> = ({
  calendar,
  calendarData,
  calendarDataSet,
  hasStockTableStyling,
  height = 600,
  hiddenColumns,
  hiddenColumnsWhenGated,
  isGated,
  noDataText,
  showAd,
  //ticker,
}) => {
  const formattedColumnsDef: ColumnDef[] = convertAgGridColumnDefsToTableColumnDefs(
    calendarData?.serverSide?.columnDef ?? [],
  );

  const colorRowByValue = calendarData?.serverSide?.colorRowByValue ?? false;
  const colorRowField = calendarData?.serverSide?.colorRowField;

  return (
    <CalendarWrapper>
      <ServerSideCalendarWrapper
        className={`${isGated && calendar !== 'insider-trades-networth' ? 'server-side-calendar-wrapper min-h-[700px]' : 'server-side-calendar-wrapper'}`}
      >
        <Table
          adPlacement={
            showAd ? (
              <BasicAdBanner
                buttonText="Sign up for Benzinga Edge"
                contentAlign="center"
                heading="CLICK HERE to join Benzinga Edge"
                subheading={
                  calendar === 'analyst-ratings'
                    ? "Unlock all major upgrades, downgrades, from the market's most accurate analysts."
                    : 'Earnings calendar plus other high-impact tools and content.'
                }
                textAlign="center"
                url={`https://www.benzinga.com/premium/ideas/benzinga-edge-checkout/?t=be8be9we4gewe1be11&utm_source=${calendar}`}
              />
            ) : undefined
          }
          colorRowByValue={colorRowByValue}
          colorRowField={colorRowField}
          columnsDef={formattedColumnsDef}
          hasStockTableStyling={hasStockTableStyling}
          height={height}
          hiddenColumns={hiddenColumns}
          hiddenColumnsWhenGated={hiddenColumnsWhenGated}
          isGated={isGated}
          noDataText={noDataText}
          rowData={calendarDataSet}
        />
      </ServerSideCalendarWrapper>
    </CalendarWrapper>
  );
};

export const CalendarWrapper = styled.div`
  width: 100%;
  .benzinga-core-table-wrapper,
  .benzinga-core-virtualized-table-wrapper {
    min-height: 400px;
    @media (max-width: 768px) {
      height: 400px;
    }
  }
  .popup-container {
    width: 100%;
    max-width: 500px !important;
  }
`;

export const ServerSideCalendarWrapper = styled.div`
  width: 100%;
  font-family: sans-serif;
  font-weight: 600;
  min-height: 180px;

  .benzinga-core-table-wrapper,
  .benzinga-core-virtualized-table-tbody {
    font-size: 12px;
  }
`;
