'use client';
import React from 'react';
import styled from '@benzinga/themetron';
import { GoLinkData, Impression } from '@benzinga/analytics';
import { AnalyticsContext, openLink } from '@benzinga/analytics';

import { MoneyBlocks } from '../MoneyBlocks';
import { CloseContainer } from '@benzinga/ui';
import { ContentBlock } from '@benzinga/content-manager';
import dayjs from 'dayjs';
import { SessionContext } from '@benzinga/session-context';

interface PopupContent extends ContentBlock {
  duration?: number;
  completion_id?: string;
}

export interface PopupProps {
  interstitial: boolean;
  popup: PopupContent;
}

export const Popup: React.FC<PopupProps> = ({ interstitial = false, popup }) => {
  const session = React.useContext(SessionContext);
  const [isVisible, setIsVisible] = React.useState(false);
  const [didDismiss, setDidDismiss] = React.useState(false);
  const [interstitialLinkData, setInterstitialLinkData] = React.useState<GoLinkData>();

  const handleOnClose = React.useCallback(() => {
    setIsVisible(false);
    setDidDismiss(true);
  }, []);

  React.useEffect(() => {
    if (!interstitial) {
      setTimeout(() => setIsVisible(true), popup.duration ?? 5000);
    }
  }, [interstitial, popup]);

  React.useEffect(() => {
    const handleFormSubmit = () => {
      if (interstitial && interstitialLinkData) {
        interstitialLinkData.event_label = `Interstitial: ${interstitialLinkData.event_label}`;
        openLink(interstitialLinkData, session);
      }
      if (popup.completion_id) {
        const date = dayjs().format('YYYY-MM-DD');
        window?.localStorage.setItem(`popup_completion_id-${popup.completion_id}`, date);
      }
      setTimeout(() => {
        setIsVisible(false);
        setDidDismiss(true);
      }, 2000);
    };
    window.addEventListener('form_submit', handleFormSubmit);
    return () => {
      window.removeEventListener('form_submit', handleFormSubmit);
    };
  }, [didDismiss, isVisible, interstitial, interstitialLinkData, popup.completion_id, session]);

  React.useEffect(() => {
    const handleExitIntent = e => {
      if (e.keyCode === 27) {
        setIsVisible(false);
        setDidDismiss(true);
      }
    };
    window.addEventListener('keydown', handleExitIntent);
    return () => {
      window.removeEventListener('keydown', handleExitIntent);
    };
  }, [isVisible, didDismiss]);

  React.useEffect(() => {
    const handleExitIntent = event => {
      if (!event.toElement && !event.relatedTarget && !interstitial) {
        if (!isVisible && !didDismiss) {
          setIsVisible(true);
        }
      }
    };
    document.addEventListener('mouseout', handleExitIntent);
    return () => {
      window.removeEventListener('mouseout', handleExitIntent);
    };
  }, [isVisible, interstitial, didDismiss]);

  React.useEffect(() => {
    const handleShowIntersitial = event => {
      const didCompleteInterstitial = window?.localStorage.getItem(`popup_completion_id-${popup.completion_id}`);

      if (!isVisible && interstitial && !didCompleteInterstitial) {
        // open interstitial url
        setIsVisible(true);
        setInterstitialLinkData(event.detail);
      } else {
        console.log('show_unmonetized_interstitial did dismiss', event);
        // openLink(event.detail, session);
      }
    };
    document.addEventListener('show_unmonetized_interstitial', handleShowIntersitial);
    // return () => {
    //   window.removeEventListener('show_unmonetized_interstitial', handleShowIntersitial);
    // };
  }, [isVisible, didDismiss, popup.completion_id, interstitial]);

  return (
    <AnalyticsContext.Provider value={{ label: 'Popup' }}>
      <PopupWrapper className={isVisible ? 'visible' : ''}>
        <CloseContainer onClose={handleOnClose}>
          <Impression campaign_id={popup.title ?? 'Unknown'} tag={'Money Popup'} unit_type="Popup CTA">
            <MoneyBlocks blocks={popup.blocks} />
          </Impression>
        </CloseContainer>
      </PopupWrapper>
    </AnalyticsContext.Provider>
  );
};

const PopupWrapper = styled.div`
  position: fixed;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  background-color: rgba(0, 0, 0, 0.5);
  padding: 1rem;
  display: none;
  overflow-y: scroll;
  z-index: 1000;

  h2.core-block,
  h3.core-block {
    margin-top: 0rem;
  }

  .call-to-action-container {
    border: none;
    box-shadow: none;
  }

  &.widget {
    .close-container {
      max-width: 750px;
    }
  }

  .close-container {
    position: relative;
    top: 10vh;
    max-width: 600px;
    margin: auto;
    background: white;
    padding: 1rem;
  }
  .call-to-action-wrapper {
    padding: 1rem;
  }
  &.visible {
    display: block;
  }
  .carousel-pagination {
    display: none !important;
  }
  .listing-preview-block {
    margin: 0;
  }
`;
