import { YouTubeVideoWordpress } from '@benzinga/videos-manager';
import { NewsSectionQueryInterface } from '@benzinga/basic-news-manager';
import type { DeviceType } from '@benzinga/device-utils';
import type { FundTopHolder } from '@benzinga/etf-manager';

type DFPValues = string | number;

export type PodcastShows = 'razreport';

export interface ArticleDFPTags {
  BZ_AUTHOR: string;
  BZ_CHANNEL: DFPValues[];
  BZ_NID: string;
  BZ_PTYPE: string;
  BZ_NEG_KEYWORD_GROUP: string[];
  BZ_POS_KEYWORD_GROUP: string[];
  BZ_TAG: DFPValues[];
  BZ_TICKER: DFPValues[];
}

export interface Block {
  attrs?: Attrs | any;
  blockName: string;
  blocks?: Block[];
  campaign?: any;
  childBlocks?: Block[];
  deviceType?: DeviceType;
  dynamic?: boolean;
  faq?: Faq[];
  headers?: Header[];
  href?: string;
  image?: any;
  innerBlocks?: any[];
  innerContent?: string[];
  innerHTML?: string;
  nodeType?: number;
  nodeID?: number | string;
  posts?: any[];
  product?: Product;
  styles?: BlockStyle[];
  tag?: string;
  top_posts?: any[];
}

export interface BlockStyle {
  type: string;
  value: string;
}

export interface ContentBlock {
  id: number;
  title: string;
  type: string;
  blocks: Block[];
  options?: LayoutHeaderOptions;
}

export interface WordpressWidget extends ContentBlock {
  canonical_link?: string;
  name?: string;
  post_content?: string;
}

export interface WordpressSidebar {
  id: number;
  slug: string;
  canonical_link?: string;
  type: string;
  title: string;
  blocks: Block[];
}

interface Attrs {
  id?: string | number;
  name?: string;
  className?: string;
  data?: AttrsData | any;
  mode?: string;
  ordered?: boolean;
  steps?: Step[];
  defaultDurationText?: string;
  level?: number;
  align?: string;
  height?: number;
  width?: number;
}

export interface AttrsData {
  cta_placeholder_type?: string;
  _cta_placeholder_type?: string;
  default_cta?: string;
  _default_cta?: string;
  product?: number;
  _product?: string;
  layout?: string;
  _layout?: string;
  product_link_type?: string;
  _product_link_type?: string;
  cta_button_text?: string;
  _cta_button_text?: string;
  show_company_name?: string;
  _show_company_name?: string;
  show_star_rating?: string;
  _show_star_rating?: string;
  show_commissions?: string;
  _show_commissions?: string;
  show_best_for?: string;
  _show_best_for?: string;
  show_pros?: string;
  _show_pros?: string;
  show_cons?: string;
  _show_cons?: string;
  hide_attributes?: string;
  _hide_attributes?: string;
  placement_identifier?: PlacementIdentifier;
  _placement_identifier?: string;
  go_link?: string;
  _go_link?: string;
  coins_number?: string;
  _coins_number?: string;
  style_faq?: string;
  _style_faq?: string;
  questions_0_question?: string;
  _questions_0_question?: string;
  questions_0_answer?: string;
  _questions_0_answer?: string;
  questions_1_question?: string;
  _questions_1_question?: string;
  questions_1_answer?: string;
  _questions_1_answer?: string;
  questions?: number;
  _questions?: string;
  cta_description?: string;
  cta_title?: string;
  video_url?: string;
}

interface PlacementIdentifier {
  placement_id: number;
}

interface Step {
  id: string;
  name: string[];
  text: Array<TextClass | string>;
  jsonName: string;
  jsonText: string;
}

interface TextClass {
  type: string;
  props: Props;
}

interface Props {
  children: any[];
}

export interface Cornerstone {
  parent_id: number;
  parent_title: string;
  parent_link: string;
  title?: string;
  link?: string;
  id?: number;
  current_post_id: number;
  current_post_title: string;
  child_posts: CornerstoneChildPost[];
}

export interface CornerstoneChildPost {
  id: number;
  title: string;
  link: string;
}

interface Faq {
  question: string;
  answer: string;
}

interface Header {
  id: string;
  level: string;
  text: string;
  html: string;
  blocks?: any[];
}

export interface ProductAttribute {
  title?: string;
  value?: string | string[] | number | number[] | null;
  key?: string;
  rating?: number | string | null;
}

export interface Details {
  best_for?: string[];
  cons?: string[];
  custom_sec?: ProductCustomSection[];
  highlight_label?: string;
  highlights?: string[];
  pros?: string[];
  desc?: string;
}

export interface ProductCustomSection {
  label: string;
  value: string;
}

export interface Product {
  attributes?: ProductAttribute[];
  company_id?: number;
  company_name?: string;
  disclosure?: string;
  best_for?: string;
  button_text?: string;
  current_promotion?: string;
  details?: Details;
  go_link_dofollow?: boolean;
  id: number;
  image: string;
  square_image?: string;
  insurance_types_of_plans?: string;
  link: string;
  name: string;
  product_badge_image?: string;
  rating?: number;
  review?: string | null;
  slug?: string;
  subtitle?: string;
  price?: string;
  phone?: string;
  teaser?: string;
  trust?: string;
  filters?: FilterAttributes;
  data?: Record<string, ProductDataAttribute>;
  tune_offer_id?: string;
  sponsored?: boolean;
}

export interface ProductResponse {
  products: Product[];
  filters: any[];
}

export interface ProductDataAttribute {
  label: string;
  value: number | string;
  display_value: string;
  sorting?: boolean;
}

export interface BenzingaProductData {
  product_pricing_monthly?: ProductDataAttribute;
  product_pricing_annual?: ProductDataAttribute;
  instructor_name?: ProductDataAttribute;
  instructor_image?: ProductDataAttribute;
}

interface FilterAttributeRange {
  min: number;
  max: number;
}

export interface FilterAttributes {
  [key: string]: string | number | FilterAttributeRange;
}

export interface Meta {
  author?: string;
  authorSlug?: string;
  description?: string;
  dimensions?: any;
  dpf?: any;
  hrefLanguage?: string;
  image?: string;
  modifiedDate?: string;
  parsely_tags?: any;
  postId?: number;
  publishedDate?: string;
  section?: string;
  title?: string;
  verifiedBy?: string;
  verifiedUserSlug?: string;
  editedBy?: string;
  editedUserSlug?: string;
}

export interface BestStock {
  stock: string;
  description: string;
  quote: any;
}

export interface BestStocks {
  stock_group_name: string;
  highlighted_stocks: BestStock[];
}

export interface Review {
  product: Product;
  form: ReviewForm;
  entries: ReviewEntry[];
  is_branded_review?: boolean;
  show_default_disclosure?: boolean;
}

export interface Breadcrumb {
  id: string | number;
  href?: string;
  name: string;
}

export interface QuickLinks {
  name: string;
  taxonomy: string;
  url_public?: string;
  path: string;
}

export interface WebStory {
  id: number;
  title: string;
  image: string;
  url: string;
}

export interface WordpressPost {
  author?: {
    name: string;
    gravatar: string;
  };
  best_stocks?: BestStocks;
  blocks?: Block[];
  breadcrumbs?: Breadcrumb[];
  campaigns?: any;
  canonical_link?: string;
  content_footer?: ContentBlock;
  content_header?: ContentBlock;
  cornerstone?: Cornerstone;
  date?: string;
  description?: string;
  entities?: Entity[];
  footer?: ContentBlock;
  global_footer: ContentBlock | null;
  header?: ContentBlock;
  id: number;
  image?: string;
  in_content?: ContentBlock;
  below_main_content?: ContentBlock;
  intro?: string;
  layout?: WordpressPostLayout;
  left_sidebar?: WordpressSidebar;
  meta?: Meta;
  parent?: Cornerstone;
  post_type?: string;
  published_at?: string;
  quicklinks?: QuickLinks[];
  review?: Review;
  robots?: string;
  sidebar_placement?: ContentBlock;
  sidebar?: WordpressSidebar;
  slug: string;
  styles?: string;
  success?: boolean;
  surrogate_keys?: string;
  type?: string;
  taxonomies?: {
    term_taxonomy_id: number;
    term_id: number;
    taxonomy: string;
    description: string;
    parent: number;
    count: number;
    term: {
      term_id: number;
      name: string;
      slug: string;
    };
  }[];
  template?: string;
  title: string;
  unmonetized_interstitial?: ContentBlock;
  url?: string;
}

export interface WordpressPostLayout {
  settings: WordpressPostLayoutSettings;
}

interface WordpressPostLayoutSettings {
  hide_article_header?: boolean;
  hide_article_footer?: boolean;
  hide_footer?: boolean;
  hide_navigation_bar: boolean;
  hide_quotes_searchbar: boolean;
  hide_site_footer: boolean;
  hide_menu_bar_items?: boolean;
  simple_article_title?: boolean;
  menu_location?: string;
  banner_text?: string;
  banner_link?: string;
}

export interface WordpressPage extends WordpressPost {
  parent_page?: WordpressPage;
}

export interface LayoutHeaderOptions {
  background_color?: string;
  full_width?: boolean;
  sticky?: boolean;
}

export interface PostHeader extends WordpressPost {
  options?: LayoutHeaderOptions;
}

export interface ReviewEntry {
  rating_item_entry_id: number;
  post_id: number;
  rating_form_id: number;
  entry_date: string;
  entry_status: string;
  title: string;
  name: string;
  comment: string;
  user_id: number;
  entries: any;
}

export interface ReviewFormItemField {
  label?: string;
  type: string;
  placeholder?: string;
  max_length?: number;
  description?: string;
  default_option_value?: number;
  max_option_value?: number;
  option_value_text?: string;
  only_show_text_options?: number;
}

export interface ReviewFormItem {
  rating_form_item_id: number;
  rating_form_id: number;
  item_id: number;
  item_type: string;
  required: number;
  allow_not_applicable: number;
  weight: number;
  field: ReviewFormItemField;
}

export interface ReviewForm {
  rating_form_id: number;
  name: string;
  items: ReviewFormItem[];
}

export interface EntityAttribute {
  label: string;
  field?: string;
  style?: string;
  type: string;
  value: any;
  locked?: boolean;
}

export interface PostsQuery {
  blockName?: string;
  category?: number;
  includeBlocks?: boolean;
  limit?: number;
  vertical?: number | string;
  page?: number;
  page_types?: number;
  post_ids?: string;
  posts_urls?: string[];
  post_feed_type?: string;
  country?: string;
  account_size?: string;
  asset_type?: string;
  initialProductIds?: string;
}

export interface LayoutResponse {
  dfp_tags: ArticleDFPTags;
  sidebar: Sidebar | null;
  header: Header;
  content_header: any | null;
  content_footer: any | null;
  footer: any | null;
  video_player?: ArticleVideoPlayer;
  disclosure_slug?: string;
  translation?: TranslationResponse;
  disable_campaignify?: boolean;
}

interface Sidebar {
  blocks: Block[];
  id: number;
  slug: string;
  title: string;
  type: string;
}

export interface Redirect {
  id: number;
  source: string;
  redirect: string;
  type: number;
  created_at?: string;
  updated_at?: string;
}

export type RedirectResponse = Redirect;

export interface Entity {
  id: number;
  attributes?: EntityAttribute[];
  data?: any;
  description?: string | null;
  status?: string;
  slug?: string;
  url?: string;
  affiliate_link?: string | null;
  name: string;
  type?: string;
  owner?: Entity;
  image?: string | null;
  issuer_website?: string;
  subtitle?: string | null;
  tags?: EntityTag[];
  ticker?: string;
  verfified?: boolean;
  created_at?: string;
  updated_at?: string;
  client_header_image?: string;
  company_deep_dive?: string;
  company_deep_dive_banner?: string;
  company_overview_banner?: string;
  is_light_header?: boolean;
  fund_holders?: FundTopHolder[] | null;
}

export interface EntityTag {
  value: string | null;
  type: string;
  variant?: 'default' | 'success';
}

export interface OfferingTake {
  text: string;
  url: string | null;
}

type ProfileSectionType = 'entities' | 'content' | 'reviews' | 'news' | 'videos' | 'chart';

export interface ProfileSection {
  entities?: Entity[];
  blocks?: Block[];
  entries?: ReviewEntry[];
  query?: NewsSectionQueryInterface;
  form?: ReviewForm;
  title: string;
  videos: YouTubeVideoWordpress[];
  symbol: string;
  exchange: string;
  type: ProfileSectionType;
}

export interface ProfileOptions {
  hide_descritption: boolean;
  hide_attributes: boolean;
  hide_call_to_action: boolean;
}

export interface LayoutNotification {
  label: string;
  link: string;
}

export interface Profile {
  breadcrumbs?: Breadcrumb[];
  entity: Entity;
  highlights?: EntityAttribute[];
  info?: EntityAttribute[];
  layout?: string;
  left_header?: any;
  meta?: Meta;
  notification?: LayoutNotification;
  options?: ProfileOptions;
  sections: ProfileSection[];
  sidebar?: WordpressSidebar;
  ticker?: string;
  title?: string;
  unmonetized_interstitial?: ContentBlock;
}

export interface ShortNode {
  id: number;
  url: string;
}

export interface Response {
  status: string;
  data: any;
}

export interface TranslationData {
  node: ShortNode;
  translations: TranslationMetaData;
}

export interface TranslationResponse extends Response {
  data: TranslationData;
}

export interface TranslationMetaData {
  [key: string]: {
    url?: string;
    post_id?: number;
  };
}

export interface PodcastsQuery {
  last_id?: number;
  limit?: number;
  next_id?: number;
  t?: number;
  show?: number;
  post_name?: string;
}

export interface PodcastPostQuery {
  post_name?: string;
}

export interface ShowsQuery {
  limit?: number;
  exclude_archive?: boolean;
}

export interface PodcastsResponse {
  data: Podcast[];
}

export interface ShowResponse {
  data: Show;
}

export interface ShowsResponse {
  data: Show[];
}

interface ShowTaxonomy {
  count: number;
  description: string;
  parent: number;
  pivot: {
    object_id: number;
    term_taxonomy_id: number;
  };
  taxonomy: string;
  term: {
    name: string;
    slug: string;
    term_group: number;
    term_id: number;
    term_order: number;
  };
  term_id: number;
  term_taxonomy_id: number;
}

export interface Podcast {
  id: number;
  date: string;
  description: string;
  duration: number;
  image: string;
  spotify_link: string;
  youtube_link: string;
  podcast_page_link: string;
  link: string;
  slug: string;
  title: string;
  show: ShowTaxonomy & {
    theme_color: string;
  };
  // url?: string; // TO REMOVE
  podcast_providers_urls: PodcastProvidersUrls;
  theme_color: string;
}

export interface PodcastPost {
  id: number;
  type: string;
  title: string;
  slug: string;
  date: string;
  content: string;
  image: string;
  image_alt: string;
  summary: string;
  duration: number;
  url: string;
  canonical_link: string;
  show_link: string;
  spotify_link: string;
  youtube_link: string | null;
  show: ShowTaxonomy;
  podcast_providers_urls: PodcastProvidersUrls;
}

export type CampaignLayout = 'default' | 'custom';

export interface NodeLayout {
  dfp_tags: ArticleDFPTags;
  sidebar: WordpressSidebar | null;
  header: Header;
  content_header: any | null;
  content_footer: any | null;
  footer: any | null;
  campaign_layout?: CampaignLayout;
  disclosure_slug?: string;
  in_content?: any;
  popup?: any;
  unmonetized_interstitial?: any;
  video_player?: ArticleVideoPlayer;
  translation?: TranslationResponse;
  disable_campaignify?: boolean;
}

export interface WordpressLayout {
  header: ContentBlock | null;
  sidebar: WordpressSidebar | null;
  above_content?: ContentBlock | null;
  content_footer?: ContentBlock | null;
  content_header?: ContentBlock | null;
  in_content: ContentBlock | null;
  below_main_content: ContentBlock | null;
  footer?: ContentBlock | null;
  layout_meta?: LayoutMeta;
}

export interface LayoutMeta {
  force_index?: boolean;
}

export interface ArticleVideoPlayer {
  connatix: ConnatixPlayerParams;
}

export interface WordpressSidebar {
  id: number;
  slug: string;
  canonical_link?: string;
  type: string;
  title: string;
  blocks: Block[];
}

export type TermId = string;

export interface Term {
  tid: TermId; // '47',
  vid: string; // '1',
  name: string; // 'ETFs',
  parent_id: string; // '0',
  type: string; // 'Channels',
  url_drupal: string; // 'taxonomy/term/47',
  url_public: string; // 'etfs'
  path?: string; // '/etfs'
}

export interface TermResponse {
  response_code: number;
  data: Term[];
}

export interface ZipCodeQuery {
  zip_code: string;
}

export interface ZipCodeResponse {
  status: string;
  data: {
    state_available: boolean;
  };
}

export interface MortgageData {
  First_Name: string;
  Last_Name: string;
  Self_Employed: boolean;
  Cell_Phone: string;
  Email_Address: string;
  Zip_Code: string;
  loan_type: string;
  property_use: string;
  property_type: string;
  loan_amount: number;
  estimated_property_value: number;
  premium: boolean;
  fico_rating?: string;
  geo: {
    area_code: number;
    organization_name: string;
    city: string;
    country_code3: string;
    continent_code: string;
    country: string;
    region: string;
    latitude: number;
    longitude: number;
    accuracy: number;
    timezone: string;
    asn: string;
    organization: string;
    country_code: string;
    ip: string;
  };
  path?: string;
  referer?: string;
}

export interface MortgageDataResponse {
  status: string;
  message?: any;
}

export interface CampaignResponse {
  campaigns: {
    [key: string]: CampaignData;
  };
}

export interface CampaignData {
  position?: string;
  cta_title: string;
  cta_description: string;
  cta_text: string;
  link_type: string;
  placement_country: string;
  placement_slug: string;
  url: string;
  utm_action: string;
  href: string;
  image_url: string;
  product_id: number;
  disclosure?: string;
  target_device_type?: string;
  cta_btn_type?: string;
  cta_btn_position: string;
  impression_tag: string;
  customize_modal_popup: boolean;
  customize_floating_popup: boolean;
  partner: string;
}

export interface PrimisMoneyBlock extends Block {
  id: string;
}

export interface CampaignifyPlaceholderBlock extends Block {
  position: string;
  nodeID: number | string;
  strategy?: string;
  ticker?: string;
}

export type MoneyBlockType = Partial<Block> | PrimisMoneyBlock | CampaignifyPlaceholderBlock;

export interface MenuItem {
  action?: string;
  highlight?: string;
  href: string;
  logged_in_href?: string;
  label?: string;
  title: string;
  img_key?: string;
}

export interface GroupItem {
  action?: string;
  category?: string;
  label: string;
  links: MenuItem[];
}

export interface SubNav {
  action?: string;
  category?: string;
  links: MenuItem[];
}

export interface MenuInterface {
  className?: string;
  color?: 'string';
  groups?: GroupItem[];
  href: string;
  logged_in_href?: string;
  label: string;
  subnav?: SubNav;
  title?: string;
  marked?: boolean;
  deviceType?: 'desktop' | 'mobile';
}

export interface Show {
  id: number;
  name: string;
  link: string;
  num_podcasts: number;
  description: string;
  slug: string;
  image: string;
  podcast_page_link: string;
  youtube_playlist: YoutubePlaylist;
  youtube_videos: YoutubeVideo[];
  spotify_show: SpotifyShow;
  spotify_playlist_id: string;
  podcast_providers_urls: PodcastProvidersUrls;
  theme_color: string | null;
}

export interface PodcastProvidersUrls {
  itunes: string | null;
  stitcher: string | null;
  tune_in: string | null;
  spotify: string | null;
  google_podcasts: string | null;
  youtube: string | null;
  discord?: string | null;
}

export interface SpotifyShow {
  id: string;
  url: string;
  description: string;
  image: string;
  name: string;
  episodes: SpotifyEpisode[];
  total_episodes: number;
}

export interface SpotifyEpisode {
  id: string;
  description: string;
  duration: number; // Milliseconds
  url: string;
  name: string;
  release_date: string;
}

export interface VertialQuery {
  alt_filter_only?: number;
  parent_id?: number;
}

export interface YoutubePlaylist {
  id: number;
  playlist_id: string;
  channel_id: string;
  title: string;
  description: string;
  status: string;
  num_videos: number;
  data: Data;
  published_at: Date;
  created_at: Date;
  updated_at: Date;
}

export interface Data {
  kind: string;
  etag: string;
  id: string;
  snippet: Snippet;
  status: Status;
  contentDetails: DataContentDetails;
}

export interface DataContentDetails {
  itemCount: number;
}

export interface Snippet {
  publishedAt: Date;
  channelId: string;
  title: string;
  description: string;
  thumbnails: Thumbnails;
  channelTitle: string;
  localized: Localized;
}

export interface Localized {
  title: string;
  description: string;
}

export interface Thumbnails {
  default: Default;
  medium: Default;
  high: Default;
  standard: Default;
  maxres: Default;
}

export interface Default {
  url: string;
  width: number;
  height: number;
}

export interface Status {
  privacyStatus: string;
}

export interface YoutubeVideo {
  id: number;
  video_id: string;
  status: string;
  title: string;
  published_at: string;
  channel_id: string;
  description: string;
  num_views: number;
  num_likes: number;
  num_dislikes: number;
  num_comments: number;
  tags: string[];
  created_at: null;
  updated_at: string;
  laravel_through_key: string;
  contentDetails: YoutubeVideoContentDetails;
}

export interface YoutubeVideoContentDetails {
  duration: string;
  dimension: string;
  definition: string;
  caption: string;
  licensedContent: boolean;
  contentRating: any[];
  projection: string;
}

export interface ConnatixPlayerParams {
  id: string; // video id
  cid: string; // client id
  pid: string; // player id
}

export interface RequestDisclosureByParams {
  channels?: string[];
  topics: string[];
  short?: boolean;
}

export interface PartnerDisclosure {
  id: number;
  slug: string;
  content: string;
  popup_content: string;
}

export type ImpressionLimitType = 'month' | 'week' | 'day';

export interface ImpressionBase {
  content_id: number;
  current_count: number;
  limit: number;
  limit_type: ImpressionLimitType;
}

export interface Impression extends ImpressionBase {
  id: number;
  created_at: string;
  updated_at: string;
}

export type CreateImpression = Omit<Impression, 'created_at | updated_at | id'>;

export type CreateImpressionsParams = { content_limitations: CreateImpression[] } | CreateImpression;

export interface GetImpressionsParams {
  content_ids: number[] | string[];
  register_impression?: boolean;
}

export interface GetImpressionResponse {
  data: Impression[];
  not_found: number[];
}

export interface RegisterImpressionResponse {
  registered: boolean;
}

export interface DentalInsuranceData {
  Cell_Phone: string;
  Email_Address: string;
  First_Name: string;
  Last_Name: string;
  Zip_Code: string;
  main_applicant: DentalInsuranceApplicant;
  relation_spouse: DentalInsuranceApplicant;
  relation_child: object;
}

export interface DentalInsuranceApplicant {
  familyRelationship?: number;
  gender?: string;
  birthdate?: string;
  isSmoker?: boolean;
  isStudent?: boolean;
  isDisabled?: boolean;
  haveSmoker?: boolean;
  haveStudent?: boolean;
  haveDisabled?: boolean;
  haveGender?: boolean;
  heading?: string;
}

export interface DentalInsuranceResponse {
  requestId: string;
  status: string;
  plans: DentalInsurancePlan[];
}

export interface DentalInsurancePlan {
  cleaning: string;
  filling: string;
  crown: string;
  planMaximum: string;
  waitingPeriods: string;
  effectiveDateDetails: string;
  dentistSearchUrl: string;
  providerSearchUrl: string;
  rootCanal: string;
  simpleExtractions: string;
  benefitsNotes: string;
  planMaximumDetails: string;
  planMaximumValue: number;
  planMax: number;
  deductible: string;
  label: string;
  planLabel: string;
  hasImplants: boolean;
  hasChildOrtho: boolean;
  hasAdultOrtho: boolean;
  hasTeethWhitening: boolean;
  order?: any;
  planId: string;
  carrierId: string;
  name: string;
  isEnabled: boolean;
  carrier: {
    carrierId: string;
    name: string;
    shortName: string;
    logoUrl: string;
    address: string;
    disclaimers: string;
  };
  planType: {
    planTypeId: number;
    productId: number;
    name: string;
  };
  premium: number;
}

export interface AskTheExpert {
  benzinga_uid?: number | null;
  email: string;
  uuid?: string | null;
  comment: string;
  ref_url?: string;
}

export interface AskTheExpertResponse {
  id: number;
  status: string;
  message: string;
}

export interface SponsoredContentArticlesParams {
  tickers: string;
}

export interface CryptoExchangeRateResponse {
  rates?: {
    [key: string]: ExchangeRate;
  };
}

export interface ExchangeRate {
  id: number;
  coin_name?: string;
  coin_symbol: string;
  compare_value: number;
  unit: string;
  type: string;
}

export interface CompareProductFieldsResponse {
  fields?: CompareProductField[];
}

export interface CompareProductField {
  key?: string;
  label?: string;
  type?: string;
  hideLabel?: boolean;
}

export interface ProductFilterOptions {
  countries?: Country[];
  asset_type?: string[];
  account_size?: string[];
}

export interface CountriesList {
  countries?: Country[];
}

export interface Country {
  code: string;
  country_name?: string;
  iso_country?: string;
}

export interface SubscribeToNewsletterRequest {
  pub_id: string;
  email: string;
  url?: string | null;
  utm_source?: string | null;
  utm_campaign?: string | null;
  utm_medium?: string | null;
}

export interface SubscribeToNewsletterResponse {
  status: string;
  message: string;
}

export interface BestofItems {
  id: number;
  b_id: string;
  name: string;
  image?: string;
  best_for?: string;
  link?: string;
  sponsored: boolean;
  btn_label?: string;
}
