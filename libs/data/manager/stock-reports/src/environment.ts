export class StockReportsEnvironment {
  public static getName = () => 'benzinga-stock-reports';
  public static getEnvironment = (env: Record<string, string>) => ({
    internalURL: new URL(env.internalURL ?? 'https://internal.api.benzinga.com/api/'),
    // url: new URL(env.url ?? 'https://api.benzinga.com/api/'),
    url: new URL(env.url ?? 'http://automated-stock-reports.datascience/api/'),
  });
}
