import { RestfulClient, Session } from '@benzinga/session';
import { SafePromise } from '@benzinga/safe-await';
import { ArticleEnvironment } from '../environment';
import {
  EditorialArticlePreviewResponse,
  ExpiredUrlResponse,
  ExpiredUrlType,
  TrendingIndiaPosts,
  TrendingIndiaTopics,
  WNSTNFollowUpQuestionsResponse,
} from '../entities/article';

export class EditorialToolsRestful extends RestfulClient {
  constructor(session: Session) {
    super(session.getEnvironment(ArticleEnvironment).editorialToolsUrl, session, {
      authorization: true,
    });
  }

  getTrendingIndiaStoriesIds = (): SafePromise<TrendingIndiaPosts[]> => {
    const url = this.URL(`api/trending-india/posts`);
    return this.get(url, {
      allowsAnonymousAuth: true,
      credentials: 'same-origin',
    });
  };

  getTrendingIndiaTopics = (): SafePromise<TrendingIndiaTopics[]> => {
    const url = this.URL(`api/trending-india/topics`);
    return this.get(url, {
      allowsAnonymousAuth: true,
      credentials: 'same-origin',
    });
  };

  getEditorialArticlePreview = (nodeId: number | string): SafePromise<EditorialArticlePreviewResponse> => {
    const key = this.session.getEnvironment(ArticleEnvironment).editorialPreviewKey;
    const hostname = this.session.getEnvironment(ArticleEnvironment).url;
    const url = this.URL(`${hostname}services/content/editorial/preview?key=${key}&nid=${nodeId}`);
    return this.get(url, {
      allowsAnonymousAuth: true,
      credentials: 'same-origin',
    });
  };

  getEditorialFollowUpsQuestionsInternal = (nodeId: number | string): SafePromise<WNSTNFollowUpQuestionsResponse> => {
    const hostname = this.session.getEnvironment(ArticleEnvironment).wnstnUrl;
    const url = this.URL(`${hostname}followups/${nodeId}`);
    return this.get(url, {
      allowsAnonymousAuth: true,
      credentials: 'same-origin',
    });
  };

  getEditorialFollowUpsQuestions = async (nodeId: number | string): SafePromise<WNSTNFollowUpQuestionsResponse> => {
    const hostname = this.session.getEnvironment(ArticleEnvironment).url;
    const url = this.URL(`${hostname}/api/wnstn-followups?articleId=${nodeId}`);
    return this.get(url, {
      allowsAnonymousAuth: true,
      credentials: 'same-origin',
    });
  };

  checkExpiredUrl = (type: ExpiredUrlType, slug: string): SafePromise<ExpiredUrlResponse> => {
    const url = this.URL(`api/v2/expired-urls/${type}/${slug}`);
    return this.get(url, {
      allowsAnonymousAuth: true,
      credentials: 'same-origin',
    });
  };
}
