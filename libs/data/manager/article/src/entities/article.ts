import { Meta, StoryCategory } from '@benzinga/advanced-news-manager';
import { DetailsQuoteType } from '@benzinga/quotes-manager';
import { MoneyBlockType, Term, Block, NodeLayout, WordpressSidebar } from '@benzinga/content-manager';
import { MetaProps } from '@benzinga/seo';

export type AuthorId = number;
export type NodeId = string | number;

export type CampaignStrategy =
  | 'none'
  | 'custom'
  | 'all'
  | 'middle'
  | 'between'
  | 'middle-bottom'
  | 'bottom'
  | 'external'
  | 'internal';

export interface Campaigns {
  top?: string;
  middle?: string;
  bottom?: string;
}

export interface Author {
  uid: AuthorId;
  name: string;
  byLine?: string;
  byLine_secondary?: string;
  profileUrl: string;
  lastname: string;
  firstname: string;
  about?: string;
  companyTitle?: string;
  companyUrl?: string;
  facebook?: string | null;
  twitter?: string | null;
  linkedin?: string | null;
  picture?: string;
}
export interface InternalTag {
  tid: number;
  vid: number;
  name: string;
  description: string;
  primary: boolean;
}

export interface InternalAssetAttributes {
  fid: string;
  filename: string;
  filepath: string;
  filesize: number;
  image_attributes: ImageAttributes;
}

export interface InternalAsset {
  type: string;
  title: string;
  alt: string;
  source: string;
  mime: string;
  primary: boolean;
  attributes: Attributes;
}

export interface InternalTranslationMetaData {
  [key: string]: {
    url?: string;
    post_id?: number;
  };
}

export interface InternalNode {
  ID: string;
  NodeID: number;
  UserID: number;
  VersionID: number;
  Type: string;
  Published: boolean;
  Quotes: InternalNodeQuotes[];
  CreatedAt: string;
  UpdatedAt: string;
  Title: string;
  Body: string;
  name: string;
  assets: InternalAsset;
  PartnerURL: string;
  TeaserText: string;
  CanonicalPath: string;
  Tags: InternalTag[];
  Tickers: InternalTag[];
  Channels: InternalTag[];
  IsBzPost: boolean;
  IsBzProPost: boolean;
  DoNotDistribute: boolean;
  Sentiment: number;
  Meta: Meta;
  Author: Author | undefined;
}

export interface InternalNodeQuotes {
  exchange: string;
  name: string;
  symbol: string;
  price: number;
  type: DetailsQuoteType;
  volume: number;
}

export interface Attributes {
  fid: string;
  filename: string;
  filepath: string;
  filesize: number;
  image_attributes: ImageAttributes;
}

export interface ImageAttributes {
  resolution: Resolution;
}

export interface Resolution {
  height: number;
  width: number;
  dpi: number;
}

export interface InternalNodeCamelCase {
  id: string;
  nodeId: number;
  userId: number;
  versionId: number;
  type: string;
  published: boolean;
  quotes: InternalNodeQuotes[];
  createdAt: string;
  updatedAt: string;
  title: string;
  body: string;
  name: string;
  assets: InternalAsset;
  partnerURL: string;
  teaserText: string;
  canonicalPath: string;
  tags: InternalTag[];
  tickers: InternalTag[];
  channels: InternalTag[];
  isBzPost: boolean;
  isBzProPost: boolean;
  doNotDistribute: boolean;
  sentiment: number;
  meta: Meta;
  image: string;
  blocks: ArticleBlock[];
  parsedBody: string[];
  author: Author | undefined;
  translationMeta?: InternalTranslationMetaData;
}

export interface GetArticleI extends InternalNode {
  image: string;
  blocks: ArticleBlock[];
  parsedBody: string[];
  translationMeta?: InternalTranslationMetaData;
  isHeadline?: boolean;
  shouldDisplayedInFrame?: boolean;
  primaryImage?: {
    alt: string;
    height: number;
    url: string;
    width: number;
  };
  Terms?: Term[];
  commentCount?: number;
}

export interface Content {
  author: string;
  body: string;
  campaign: string;
  created: string;
  id: number;
  image: string;
  imageLarge: string;
  image_thumb: string;
  teaser: string;
  title: string;
  updated: string;
  url: string;
  tags: { name: string }[];
}

export interface Image {
  alt?: string;
  size?: string;
  url: string;
  height?: number;
  width?: number;
}

export interface Stock {
  name: string;
}

export interface News {
  author: string;
  body: string;
  channels: StoryCategory[];
  created: string;
  id: number;
  image: Image[];
  stocks: Stock[];
  tags: StoryCategory[];
  teaser: string;
  title: string;
  updated: string;
  url: string;
}

export enum SourceId {
  Story = 'story',
}

export enum ResourceType {
  articles = 'articles',
  content = 'content',
}

export interface Campaigns {
  bottom?: string;
  middle?: string;
  top?: string;
}

export interface ExtractedArticleBlock {
  block: ArticleBlock;
  index: number;
}

export interface ArticleBlock extends Block {
  blockName: string;
  blocks?: ArticleBlock[];
  childBlocks?: ArticleBlock[];
  dynamic?: boolean;
  extracted?: ExtractedArticleBlock[];
  hasChild: number | boolean;
  innerHTML: string;
  tagAttributes: Record<string, string | null>;
  type?: string;
}

export interface ArticleBlockPlaceholder {
  blockName: string;
  name: string;
}

export type ArticleBlocksList = ArticleBlock | MoneyBlockType;

export interface translationMetaData {
  [key: string]: {
    url?: string;
    post_id?: number;
  };
}

type DFPValues = string | number;

export interface ArticleDFPTags {
  BZ_AUTHOR?: string;
  BZ_CHANNEL?: DFPValues[];
  BZ_NID?: string;
  BZ_PTYPE?: string;
  BZ_NEG_KEYWORD_GROUP?: string[] | null;
  BZ_POS_KEYWORD_GROUP?: string[] | null;
  BZ_TAG?: DFPValues[];
  BZ_TICKER?: DFPValues[];
  newTemplate?: boolean;
}

export interface ArticleData {
  dfpTags?: ArticleDFPTags;
  assets: InternalAsset;
  author?: Author; // TODO check if this is actually available
  authorChange?: string;
  body: string;
  parsedBody: string[];
  campaigns?: Campaigns;
  campaignStrategy?: CampaignStrategy;
  canonicalPath?: string;
  relatedArticles?: News[];
  channels: StoryCategory[];
  createdAt: string;
  id: number | string;
  image: string;
  primaryImage?: null | Image;
  layout?: NodeLayout | null;
  imageLarge?: string;
  isHeadline?: boolean;
  isBzPost?: boolean;
  isBzProPost?: boolean;
  keyItems?: { value: string }[];
  meta: Meta;
  metaProps?: MetaProps;
  profileUrl?: string;
  headStyles?: string;
  name: string;
  nodeId?: number;
  quotes: InternalNodeQuotes[];
  sidebar?: WordpressSidebar;
  tags: StoryCategory[];
  terms?: Term[];
  teaserText: string;
  tickers: StoryCategory[];
  title: string;
  titleSeo?: string;
  type: string;
  updatedAt: string;
  url?: string;
  blocks: ArticleBlock[] | ArticleBlockPlaceholder[];
  translationMeta?: translationMetaData;
  shouldDisplayedInFrame?: boolean;
  commentCount?: number;
}

export interface AuthorElement {
  uid: string;
  name: string;
}

export interface RelevantArticles {
  sponsored?: ArticleData[];
  topStories?: ArticleData[];
}

export interface RelevantArticlesIds {
  sponsored?: number[];
  topStories?: number[];
}

export interface DraftArticle {
  blocks: ArticleBlock[];
  body: string;
  channels: Taxonomy[];
  image: string | null;
  name: string;
  nodeId: number;
  tags: Taxonomy[];
  teaserText: string;
  tickers: Taxonomy[];
  title: string;
  createdAt: string;
  key_items: FieldAccessRestricted[];
}

export interface TrendingIndiaPosts {
  WPID: number;
  nid: string;
}

export interface TrendingIndiaTopics {
  name: string;
  taxonomy: Taxonomy[];
  url_public: string;
}

export interface Metrics {
  views: number;
  visitors: number;
}

export interface EditorialArticlePreviewResponse {
  response_code: number;
  data: EditorialArticlePreview;
}

export interface EditorialArticlePreview {
  nid: number;
  type: string;
  language: string;
  uid: number;
  status: number;
  created: number;
  changed: number;
  comment: number;
  promote: number;
  moderate: number;
  sticky: number;
  tnid: number;
  translate: number;
  is_bz_post: number;
  is_bzpro_post: number;
  count_char: number;
  count_word: number;
  vid: number;
  revision_uid: number;
  title: string;
  body: string;
  teaser: string;
  log: string;
  revision_timestamp: number;
  format: number;
  name: string;
  picture: string;
  data: string;
  path: string;
  field_seo_title: FieldAccessRestricted[];
  field_copyright: FieldAccessRestricted[];
  field_key_items: FieldAccessRestricted[];
  field_image: FieldImage[];
  field_is_feed: FieldAccessRestricted[];
  field_top_picks: FieldAccessRestricted[];
  field_is_bzpro_post: FieldAccessRestricted[];
  field_is_bz_post: FieldAccessRestricted[];
  field_contributed_content: FieldAccessRestricted[];
  field_access_restricted: FieldAccessRestricted[];
  field_is_slideshow: FieldAccessRestricted[];
  field_slideshow_bottom_link: FieldSlideshowBottomLink[];
  field_slideshow_start_link: FieldAccessRestricted[];
  field_stock_chart: null[];
  field_evergreen_content: FieldAccessRestricted[];
  field_partner_content_url: FieldAccessRestricted[];
  field_google_standout_tag: FieldAccessRestricted[];
  field_rate_bull_bear: FieldAccessRestricted[];
  field_primay_tickers_custom: FieldAccessRestricted[];
  field_story_post_status: FieldAccessRestricted[];
  field_show_advertiser_disclosure: FieldAccessRestricted[];
  field_story_post_created: FieldAccessRestricted[];
  field_do_not_distribute: FieldAccessRestricted[];
  field_dnd_override_media: FieldAccessRestricted[];
  field_send_to_yahoo_finance: FieldAccessRestricted[];
  field_send_to_robinhood: FieldAccessRestricted[];
  field_bz_story_body_length: FieldAccessRestricted[];
  field_slideshow_images: null[];
  _workflow: number;
  meta: Meta;
  price: Price[];
  disqus: Disqus;
  premium: number;
  premium_access: boolean;
  taxonomy: Taxonomy[];
  files: any[];
  is_partner: number;
  field_top_picks_value: number;
  field_is_bzpro_post_value: number;
  field_is_bz_post_value: number;
  is_dnd: number;
  syndicate_url: null;
  key_items: FieldAccessRestricted[];
}

export interface Disqus {
  domain: string;
  status: boolean;
  url: string;
  title: string;
  identifier: string;
}

export interface FieldAccessRestricted {
  value: number | null | string;
}

export interface FieldImage {
  fid: string;
  filename: string;
  url: string;
  status: number;
  filesize: string;
  filepath: string;
  data: FieldImageData;
  list: null;
}

export interface FieldImageData {
  alt: string;
  title: string;
  source: null;
}

export interface FieldSlideshowBottomLink {
  url: null;
  title: null;
  attributes: boolean;
}
export interface SectorV2 {
  SIC: Sic[];
  NAICS: Naic[];
  Morningstar: Morningstar[];
}

export interface Morningstar {
  IndustryCode: number;
  Industry: string;
  IndustryGroupCode: number;
  IndustryGroup: string;
  SectorCode: number;
  Sector: string;
  SuperSectorCode: number;
  SuperSector: string;
}

export interface Naic {
  NationalIndustryCode: number;
  NationalIndustry: string;
  IndustryCode: number;
  Industry: string;
  IndustryGroupCode: number;
  IndustryGroup: string;
  SubSectorCode: number;
  SubSector: string;
  SectorCode: number;
  Sector: string;
}

export interface Sic {
  IndustryCode: number;
  Industry: string;
  IndustryGroup: number;
  MajorGroup: number;
  Division: string;
}

export interface WpPostMeta {
  post_path: string;
  post_id: number;
  post_type: string;
}

export interface Price {
  tid: string;
  price: string;
  volume: string;
}

export interface Taxonomy {
  tid: number;
  vid: number;
  name: string;
  description: string;
  weight: string;
  v_weight_unused: string;
}

export interface WNSTNFollowUpQuestionsResponse {
  status: string;
  node_id: string;
  post_id: number;
  created_at: string;
  article_index: string;
  follow_up_questions: string[];
}

export interface ExpiredUrlData {
  id: number;
  slug: string;
  type: string;
  is_active: number;
  created_at: string;
  updated_at: string;
}

export interface ExpiredUrlResponse {
  data: ExpiredUrlData | null;
  message: string;
  status: 'success' | 'error';
}

export type ExpiredUrlType = 'topic' | 'article' | 'quote';
