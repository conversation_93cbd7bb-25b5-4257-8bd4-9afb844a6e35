import { SafePromise } from '@benzinga/safe-await';
import { Subscribable } from '@benzinga/subscribable';

import {
  GovernmentTrade,
  GovernmentMember,
  MonthlyOverview,
  Overview,
  Security,
  SecuritiesOverview,
  TradingPerformance,
  MembersOverview,
} from './entities';
import { GovTradesRequest, GovTradesRestfulEvent } from './request';
import { Session } from '@benzinga/session';

// an issue with elasticsearch can cause the endpoints to return duplicate data
// this function is to filter for the latest doc using updated
export const getMostUpdated = (arr: any[]) => {
  if (!arr || arr.length === 0) return null;
  if (arr.length === 1) return arr[0];
  return arr.reduce((acc, curr) => {
    return acc.updated > curr.updated ? acc : curr;
  });
};

export type GovTradesManagerEvent = GovTradesRestfulEvent;

export class GovTradesManager extends Subscribable<GovTradesManagerEvent> {
  private request: GovTradesRequest;

  constructor(session: Session) {
    super();
    this.request = new GovTradesRequest(session);
  }

  public static getName = () => 'benzinga-gov-trades';

  public getTrades = async (
    pageSize: number | undefined,
    page: number | undefined,
  ): SafePromise<{ data: GovernmentTrade[] }> => {
    const response = await this.request.getTrades(pageSize, page);
    if (response.ok) {
      return {
        ok: {
          data: response?.ok.data,
        },
      };
    } else {
      return { err: response.err };
    }
  };

  public getMembers = async (): SafePromise<MembersOverview> => {
    const response = await this.request.getMembers();
    if (response.ok) {
      return { ok: getMostUpdated(response?.ok) };
    } else {
      return { err: response.err };
    }
  };

  public getMember = async (id: string): SafePromise<GovernmentMember> => {
    const response = await this.request.getMember(id);
    if (response.ok) {
      return { ok: getMostUpdated(response?.ok) };
    } else {
      return { err: response.err };
    }
  };

  public getReports = async (): SafePromise<{ data: GovernmentTrade[] }> => {
    const response = await this.request.getReports();
    if (response.ok) {
      return { ok: response?.ok };
    } else {
      return { err: response.err };
    }
  };

  public getOverview = async (): SafePromise<Overview[]> => {
    const response = await this.request.getOverview();
    if (response.ok) {
      return { ok: getMostUpdated(response?.ok) };
    } else {
      return { err: response.err };
    }
  };

  public getOverviewMonthly = async (): SafePromise<MonthlyOverview[]> => {
    const response = await this.request.getOverviewMonthly();
    if (response.ok) {
      return { ok: getMostUpdated(response?.ok) };
    } else {
      return { err: response.err };
    }
  };

  public getSecurity = async (id: string): SafePromise<Security> => {
    const response = await this.request.getSecurity(id);
    if (response.ok) {
      return { ok: getMostUpdated(response?.ok) };
    } else {
      return { err: response.err };
    }
  };

  public getSecurities = async (): SafePromise<SecuritiesOverview> => {
    const response = await this.request.getSecurities();
    if (response.ok) {
      return { ok: getMostUpdated(response?.ok) };
    } else {
      return { err: response.err };
    }
  };

  public getCongressTradingPerformance = async (): SafePromise<TradingPerformance> => {
    const response = await this.request.getCongressTradingPerformance();
    if (response.ok) {
      return { ok: getMostUpdated(response?.ok) };
    } else {
      return { err: response.err };
    }
  };

  public getTradingPerformanceByMember = async (id: string): SafePromise<TradingPerformance> => {
    const response = await this.request.getTradingPerformanceByMember(id);
    if (response.ok) {
      return { ok: getMostUpdated(response?.ok) };
    } else {
      return { err: response.err };
    }
  };

  public getSecuritiesList = async (): SafePromise<Security[]> => {
    const response = await this.request.getSecurities();
    if (response.ok) {
      const securitiesOverview = getMostUpdated(response?.ok);
      const list = securitiesOverview?.most_active?.map(security => {
        return {
          ...security,
          ticker: security.symbol,
        };
      });
      return { ok: list };
    } else {
      return { err: response.err };
    }
  };
}
