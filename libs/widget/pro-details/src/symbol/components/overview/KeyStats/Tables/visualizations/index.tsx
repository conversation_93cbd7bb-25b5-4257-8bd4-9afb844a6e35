'use client';
/* eslint-disable @typescript-eslint/no-empty-function */
import { AnalystRatings } from '@benzinga/analyst-ratings-visualization';
import { createSymbolSearchItem } from '@benzinga/search-modules';
import { Title } from '../shared';
import { StockSymbol } from '@benzinga/session';
import { GuageChart } from '@benzinga/guage-visualization';
import styled from 'styled-components';
import { ThemeContext } from '@benzinga/themetron';
import React from 'react';

interface VisualizationsTableProps {
  symbol: StockSymbol;

  handleVisualizationClick?: () => void;
}

const ColorKey = () => {
  const theme = React.useContext(ThemeContext);
  const allColors = React.useMemo(
    () => [
      { color: theme.colors.statistic.positive, label: 'Strong Buy' },
      { color: '#80ffe5', label: 'Buy' },
      { color: theme.colors.foregroundInactive, label: 'Hold' },
      { color: '#ff887f', label: 'Sell' },
      { color: theme.colors.statistic.negative, label: 'Strong Sell' },
    ],
    [theme.colors.foregroundInactive, theme.colors.statistic.negative, theme.colors.statistic.positive],
  );

  return (
    <ColorKeyWrapper>
      {allColors.map(color => (
        <ColorLabelWrapper key={color.label}>
          <div className="color-key-box" style={{ background: color.color }}></div>
          <div className="text-sm">{color.label}</div>
        </ColorLabelWrapper>
      ))}
    </ColorKeyWrapper>
  );
};

const ColorLabelWrapper = styled.div`
  display: flex;
  align-items: center;
  gap: 4px;
  flex-wrap: wrap;
`;

export const VisualizationsTable: React.FC<VisualizationsTableProps> = props => {
  return (
    <div className="KeyStat KeyStat--comparison">
      <Title clickByTitle={props.handleVisualizationClick} hasMoreLink={true} name="Analyst Trends" />
      <StyledAnalystRatingsVisualizationGroup>
        <ChartWrapper>
          <StyledRatingsWrapper>
            <AnalystRatings
              args={{ layout: { showTitleBar: false }, traces: [] }}
              flightMode={false}
              onChange={() => {}}
              onSourceChanged={() => {}}
              sources={[props.symbol].map(s => createSymbolSearchItem(s))}
            />
          </StyledRatingsWrapper>
          <StyledGaugeChartWrapper>
            <GuageChart
              args={{ global: {}, layout: { mode: 'combined', period: 'Current' }, traces: [] }}
              flightMode={false}
              onChange={() => {}}
              onSourceChanged={() => {}}
              sources={[props.symbol].map(s => createSymbolSearchItem(s))}
            />
          </StyledGaugeChartWrapper>
        </ChartWrapper>
        <ColorKey />
      </StyledAnalystRatingsVisualizationGroup>
    </div>
  );
};

const StyledAnalystRatingsVisualizationGroup = styled.div`
  display: flex;
  flex-direction: column;
  margin-top: 10px;
  width: 100%;
`;
const ColorKeyWrapper = styled.div`
  .color-key-box {
    height: 6px;
    width: 30px;
  }
  width: 100%;
  display: flex;
  flex-wrap: wrap;
  margin-top: 10px;
  gap: 10px;
  justify-content: center;
  background: ${({ theme }) => theme.colors.background};
`;

const StyledRatingsWrapper = styled.div`
  display: flex;
  align-items: center;
  justify-content: center;
  width: 50%;
`;
const StyledGaugeChartWrapper = styled.div`
  width: 50%;
`;
const ChartWrapper = styled.div`
  width: 100%;
  min-height: 150px;
  max-height: 200px;
  display: flex;
  gap: 1.5em;
`;
