'use client';
import React from 'react';

import { Story } from '@benzinga/advanced-news-manager';

import { getParentLinkNode, isLinkingCurrentPage, isSymbolClick } from '../utils/domUtils';
import NewsfeedContent from './content';
import { NewsfeedStoryHeadlines } from './storyHeadlines';
import { SessionContext } from '@benzinga/session-context';
import { popUpStory } from '../utils/popup';
import { IframeContainer } from './styles';
import { buildKeyPhrasesRegex } from '../utils/highlight';
import Hooks from '@benzinga/hooks';
import { LoggingManager } from '@benzinga/session';
import { TrackingManager } from '@benzinga/tracking-manager';
import { openWindow } from '@benzinga/frontend-utils';
import { ArticleManager } from '@benzinga/article-manager';
import { SendLinkContext } from '@benzinga/pro-ui';
import { useNewsfeedGlobalSettings } from '../utils/useGlobalSettings';
import { DisplayType } from '@benzinga/news-user-settings';
import { useHeadlineImpression } from '../utils/useImpressions';

const ROW_TOOLS_TRACKING_MESSAGE = 'Story Popout Using Row Tools';
const MAX_IFRAME_HEIGHT = 500;

interface Props {
  displayType: DisplayType;
  isFresh: boolean;
  narrowWidthLayout: boolean;
  paddingStyle: string;
  showStoryTools: boolean;
  story: Story;
  widgetWidth?: number;
  widgetID?: string;
  textSize: number;
  computeHeight: () => void;
}

export const useNewsItemOnLinkClick = () => {
  const sendLink = React.useContext(SendLinkContext);

  const onLinkClick = React.useCallback(
    (event: React.MouseEvent<HTMLElement>) => {
      const anchor = getParentLinkNode(event.target as HTMLElement);
      if (!anchor) {
        return;
      }

      if (isSymbolClick(anchor)) {
        event.preventDefault();
        event.stopPropagation();
        const symbol = anchor.innerText;

        sendLink.onSymbolClick(symbol);
        return;
      }

      const href = anchor.href as string | undefined;
      if (isLinkingCurrentPage(href)) {
        return;
      }

      event.preventDefault();

      // Chrome doesn't support tabs if any settings are passed. Must use _blank
      // ToDo: Eventually replace this with a library of browsers, os and mobile...
      if (navigator.userAgent.toLowerCase().includes('chrome')) {
        openWindow(href, '_blank');
        return;
      }
      // Set the default toolbar
      const name = '_blank';
      const toolBar = [
        'resizable=yes',
        'scrollbars=yes',
        'toolbar=yes',
        'menubar=no',
        'location=no',
        'directories=no',
        'status=1',
        'statusbar=1',
      ].join(',');
      openWindow(href, name, toolBar);
    },
    [sendLink],
  );
  return onLinkClick;
};

export const NewsfeedStory: React.FC<Props> = React.memo(props => {
  const Article = React.useRef<HTMLDivElement>(null);
  const IFrameContainer = React.useRef<HTMLDivElement>(null);
  const iframeHeight = React.useRef<number | undefined>(undefined);
  const newsfeedGlobalSettingsContext = useNewsfeedGlobalSettings();
  const session = React.useContext(SessionContext);
  const articleManager = session.getManager(ArticleManager);

  const { highlightDollarAmounts, highlightPercentages, highlightQuarters } = newsfeedGlobalSettingsContext;

  const keyPhrasesRegex = React.useMemo(
    () => buildKeyPhrasesRegex(highlightDollarAmounts, highlightPercentages, highlightQuarters),
    [highlightDollarAmounts, highlightPercentages, highlightQuarters],
  );

  const popoutStory = React.useCallback(() => {
    const nodeId = props.story.getNodeId();
    const title = props.story.getTitle();
    session.getManager(TrackingManager).trackWidgetEvent('click', 'newsfeed_widget', {
      button_id: 'row_tools_popout',
      button_value: `${nodeId.toString()} - ${title}`,
    });
    popUpStory(props.story, keyPhrasesRegex, articleManager);
  }, [props.story, session, keyPhrasesRegex, articleManager]);

  const onLinkClick = useNewsItemOnLinkClick();

  const debugStory = React.useCallback(() => {
    session.getManager(LoggingManager).log(
      'log',
      {
        category: 'Newsfeed',
        data: props.story,
        message: `nodeId(${props.story.getNodeId()})`,
      },
      ['console'],
    );

    session.getManager(TrackingManager).trackWidgetEvent('click', 'newsfeed_widget', {
      button_id: 'copy_title',
      button_value: `${(props.story.getNodeId() || 0).toString()} - ${props.story.getTitle()}`,
    });
  }, [props.story, session]);

  const updateIframeHeight = React.useCallback(
    (height: number) => {
      // the reason fro the following check is because child components finish rendering first
      // this results in IFrameContainer not being set when this function is called. hence we have to
      // store the value and call this function at a later time.
      if (!IFrameContainer.current) {
        iframeHeight.current = height;
      } else if (IFrameContainer.current.style.height !== `${Math.min(height, MAX_IFRAME_HEIGHT)}px`) {
        IFrameContainer.current.style.height = `${Math.min(height, MAX_IFRAME_HEIGHT)}px`;
        const computeHeight = props.computeHeight;
        computeHeight();
      }
    },
    [props.computeHeight],
  );

  const renderBody = React.useMemo(
    () => (
      <RenderBody
        className="NewsfeedStory-body"
        computeHeight={props.computeHeight}
        onLinkClick={onLinkClick}
        ref={IFrameContainer}
        story={props.story}
        updateIframeHeight={updateIframeHeight}
      ></RenderBody>
    ),
    [onLinkClick, props.computeHeight, props.story, updateIframeHeight],
  );

  React.useEffect(() => {
    if (iframeHeight.current) {
      updateIframeHeight(iframeHeight.current);
      iframeHeight.current = undefined;
    }
  });

  const {
    computeHeight,
    displayType,
    isFresh,
    narrowWidthLayout,
    paddingStyle,
    showStoryTools,
    story,
    textSize,
    widgetWidth,
  } = props;

  if (!story) {
    return null;
  }

  const commonProps = {
    allCaps: newsfeedGlobalSettingsContext.allCaps,
    articleRef: Article,
    computeHeight,
    debugStory: debugStory,
    onLinkClick: onLinkClick,
    popoutStory: popoutStory,
    showStoryTools,
    story,
    textSize,
    widgetID: props.widgetID,
    widgetWidth,
  };

  return (
    <NewsfeedStoryHeadlines
      {...commonProps}
      displayType={displayType}
      isFresh={isFresh}
      narrowWidthLayout={narrowWidthLayout}
      paddingStyle={paddingStyle}
      renderBody={renderBody}
      sourceShortName={story.getSource()?.shortName ?? ''}
    />
  );
});

interface RenderBodyProps {
  className?: string;
  story: Story;
  updateIframeHeight?: (height: number) => void;
  onLinkClick?: (event: React.MouseEvent<HTMLElement>) => void;
  computeHeight(): void;
}
export const RenderBody = React.forwardRef<HTMLDivElement, RenderBodyProps>((props, ref) => {
  const [storyBody, setStoryBody] = React.useState(() => props.story.getBody() ?? '');
  Hooks.useSubscriber(props.story, () => {
    setStoryBody(props.story.getBody() ?? '');
  });

  React.useEffect(() => {
    if (props.story.getBody() === undefined) {
      props.story.fetchBody();
    }
  }, [props.story]);

  if (props.story.viewWithIframe()) {
    return (
      <IframeContainer ref={ref}>
        <NewsfeedContent
          computeHeight={props.computeHeight}
          content={storyBody}
          iframe
          onIframeRendered={props.updateIframeHeight}
          onLinkClick={props.onLinkClick}
          story={props.story}
          storyBody
          wrapLinks
        />
      </IframeContainer>
    );
  }
  return props.story.getBody() ? (
    <div className={props.className}>
      <NewsfeedContent
        computeHeight={props.computeHeight}
        content={props.story.getBody() ?? ''}
        story={props.story}
        storyBody
        wrapLinks
      />
    </div>
  ) : (
    <></>
  );
});
