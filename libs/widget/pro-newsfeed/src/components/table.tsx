'use client';
import React, { useCallback, useEffect } from 'react';
import { CellMeasurer, CellMeasurerCache, List, ListRowProps, WindowScroller, InfiniteLoader } from 'react-virtualized';

import Hooks from '@benzinga/hooks';
import { Spinner } from '@benzinga/core-ui';
import styled, { TC } from '@benzinga/themetron';
import { SessionContext } from '@benzinga/session-context';
import { AdvancedNewsManager, AdvancedNewsManagerEvent, NewsFeedEvents } from '@benzinga/advanced-news-manager';

import { NoResults } from '@benzinga/pro-ui';
import { NewsfeedStoryContainer } from './storyContainer';
import { AdvancedNewsfeedContext } from '../context/feedContext';
import { NewsfeedInternalSettingsContext } from '../context/internalSettingContext';
import { AdvancedNewsfeedContainerEvent, AdvancedNewsfeedItem } from '../utils/container';
import { WidgetContext } from '@benzinga/widget-tools';
import { useNewsfeedGlobalSettings } from '../utils/useGlobalSettings';
import { DisplayType } from '@benzinga/news-user-settings';
import { useNewsfeedTable } from '../utils/useNewsfeedTable';

interface Props {
  displayType?: DisplayType;
  parentElement: Element;
}

interface Handle {
  scrollToTop: () => void;
}

export type NewsfeedTableHandle = Handle;

// This is an estimate and doesn't actually represent the height of each row.
const DEFAULT_ROW_HEIGHT = 25; // px

export const NewsfeedTable = React.memo(
  React.forwardRef<Handle, Props>((props, ref) => {
    const session = React.useContext(SessionContext);
    const internalSettings = React.useContext(NewsfeedInternalSettingsContext);
    const globalSettings = useNewsfeedGlobalSettings();
    const paddingStyle = React.useMemo(
      () => `${globalSettings.headerSpacing ?? 1}px 0px`,
      [globalSettings.headerSpacing],
    );
    const narrowWidthLayout = React.useRef(true);
    const setScroll = React.useRef<number | undefined>(undefined);
    const noMorePastHistory = React.useRef(false);
    const noMoreFutureHistory = React.useRef(!internalSettings.jumpedToDate);
    const hasRequestedPastHistory = React.useRef(true);
    const hasRequestedFutureHistory = React.useRef(false);
    const prevWidth = React.useRef<undefined | number>(undefined);
    const {
      historicLimit = 100,
      newsfeed = undefined,
      newsfeedContainer = undefined,
    } = React.useContext(AdvancedNewsfeedContext) ?? {};
    const prevNewsFeedContainer = Hooks.usePrevious(newsfeedContainer);
    const update = Hooks.useForceUpdate();
    const lastScrolledPosition = React.useRef(0);
    const widgetContext = React.useContext(WidgetContext);
    const widgetWidth = widgetContext.getClientWidth();
    const widgetID = widgetContext.widgetId;
    const displayFirstDate = React.useMemo(() => !globalSettings.displayDateBanner, [globalSettings.displayDateBanner]);
    const { onRowsRenderedHandle } = useNewsfeedTable({ parentElement: props.parentElement, widgetID });

    const [endMessage, setEndMessage] = React.useState<{
      message: string;
      state: 'disconnected' | 'connected' | 'error';
    }>({ message: 'End of Newsfeed', state: 'connected' });

    const createCache = React.useCallback(
      () =>
        new CellMeasurerCache({
          defaultHeight: DEFAULT_ROW_HEIGHT,
          fixedWidth: true,
          keyMapper: (rowIndex: number, _columnIndex: number): string => {
            if (newsfeedContainer) {
              const item =
                newsfeedContainer.getDisplayItemsFormatted(displayFirstDate)[
                  newsfeedContainer.getDisplayItemsFormattedLength(displayFirstDate) - 1 - rowIndex
                ];
              if (item === undefined) {
                return '';
              }
              switch (item.type) {
                case 'story':
                  return item.story.getStoryId();
                case 'date':
                  return item.displayDate;
              }
            } else {
              return '';
            }
          },
          minHeight: DEFAULT_ROW_HEIGHT,
        }),
      [displayFirstDate, newsfeedContainer],
    );

    const cache = React.useRef<CellMeasurerCache | null>(createCache());
    if (cache.current === null) {
      cache.current = createCache();
    }

    const currentCache: CellMeasurerCache = cache.current;

    const setScrollRow = React.useCallback(
      (scrollRow: number) => {
        setScroll.current = scrollRow;
        update();
      },
      [update],
    );

    if (prevNewsFeedContainer !== newsfeedContainer) {
      hasRequestedPastHistory.current = true;
      noMoreFutureHistory.current = !internalSettings.jumpedToDate;
      hasRequestedFutureHistory.current = false;
    }

    const indexFromScrollTop = React.useMemo(
      () => (scrollTop: number) => {
        scrollTop = scrollTop >= 0 ? scrollTop : 0;
        if (!cache.current) return 0;
        let index = 0;
        let total = 0;
        while (total < scrollTop) {
          index++;
          total += cache.current.getHeight(index, 0);
        }
        if (total === scrollTop) return index;
        return index - 1;
      },
      [cache],
    );

    Hooks.useSubscriber(session.getManager(AdvancedNewsManager), (event: AdvancedNewsManagerEvent) => {
      switch (event.type) {
        case 'news:socket_errors':
          setEndMessage({
            message: event.errors.map(error => `${error.code}`).join('\n'),
            state: 'error',
          });
          console.error(event.errors);
          noMorePastHistory.current = true;
          noMoreFutureHistory.current = internalSettings.jumpedToDate === null;
          break;
      }
    });

    const updateMessage = () => {
      setEndMessage(old => {
        if (old.message === 'No results found') {
          if (newsfeedContainer?.getDisplayItemsFormattedLength(displayFirstDate) === 0) {
            return old;
          } else {
            return { message: 'End of Newsfeed', state: 'connected' };
          }
        } else if (old.message === 'End of Newsfeed') {
          if (newsfeedContainer?.getDisplayItemsFormattedLength(displayFirstDate) === 0) {
            return { message: 'No results found', state: 'connected' };
          } else {
            return old;
          }
        } else {
          return old;
        }
      });
    };

    Hooks.useSubscriber(newsfeed, (event: NewsFeedEvents) => {
      switch (event.type) {
        case 'news:past_historic_stories':
          noMorePastHistory.current = !event.hasMoreHistory;
          updateMessage();
          break;
        case 'news:future_historic_stories':
          noMoreFutureHistory.current = !event.hasMoreHistory;
          updateMessage();
          break;
        case 'news:reconnected':
          setEndMessage({ message: 'Connected. scroll up then down to get more history', state: 'connected' });
          break;
        case 'news:disconnected':
          setEndMessage({ message: 'Newsfeed disconnected: trying to reconnect', state: 'disconnected' });
          break;
      }
    });

    const dateStringFromItem = React.useCallback((item: AdvancedNewsfeedItem | null) => {
      if (!item) return null;
      if (item.type === 'date') {
        return item.displayDate;
      } else if (item.type === 'story') {
        return item.story.getDisplayDateString();
      } else {
        return null;
      }
    }, []);

    const [setFirstScrolledToItem, resetFirstScrolledToItem] = Hooks.useOnce(() => {
      internalSettings.setBannerDate?.(
        dateStringFromItem(
          newsfeedContainer?.getDisplayItemsFormatted(displayFirstDate)?.[
            newsfeedContainer?.getDisplayItemsFormattedLength(displayFirstDate) - 1
          ] ?? null,
        ),
      );
    });

    const [newsfeedContainerSubscription] = Hooks.useSubscriber(
      newsfeedContainer,
      (event: AdvancedNewsfeedContainerEvent) => {
        switch (event.type) {
          case 'historic_update':
            setFirstScrolledToItem();
            hasRequestedPastHistory.current = false;
            update();
            break;

          case 'future_update':
            setTimeout(() => {
              hasRequestedFutureHistory.current = false;
            }, 0);
            event.transaction.add && setScrollRow(event.transaction.add.length + lastScrolledPosition.current);

            update();
            break;
          case 'live_update': {
            update();
            break;
          }
          case 'clear':
            hasRequestedPastHistory.current = false;
            hasRequestedFutureHistory.current = false;
            update();
            break;
        }
      },
    );

    newsfeedContainerSubscription?.pause();

    React.useEffect(() => {
      newsfeedContainerSubscription?.resume();
      setScroll.current = undefined;
    });

    React.useEffect(() => {
      if (newsfeedContainer?.getDisplayItemsFormattedLength(displayFirstDate) === 0) {
        newsfeedContainer?.getHistoric(historicLimit);
      }
    }, [displayFirstDate, historicLimit, newsfeedContainer]);

    React.useImperativeHandle(
      ref,
      () => ({
        scrollToTop: () => setScrollRow(0),
      }),
      [setScrollRow],
    );

    // This is called per row and should be as light as possible
    const rowRenderer = React.useCallback(
      ({ index, key: paramKey, parent, style }: ListRowProps) => {
        // We are on the last row if rowId is undefined.
        // This is because we add 1 to the rowcount, so the last row will be outside the bounds of the array
        if (newsfeedContainer) {
          const item =
            index < newsfeedContainer.getDisplayItemsFormattedLength(displayFirstDate)
              ? newsfeedContainer.getDisplayItemsFormatted(displayFirstDate)[
                  newsfeedContainer.getDisplayItemsFormattedLength(displayFirstDate) - 1 - index
                ]
              : undefined;
          const isLastRow = newsfeedContainer?.getDisplayItemsFormattedLength(displayFirstDate) === index;
          const isFirstRow = 0 === index && !noMoreFutureHistory.current;
          const key = item
            ? item.type === 'story'
              ? item.story.getStoryId()
              : item.type === 'date'
                ? item.displayDate
                : paramKey
            : 'archiveStorySpinner';

          return (
            <CellMeasurer cache={currentCache} columnIndex={0} key={key} parent={parent} rowIndex={index}>
              {({ measure, registerChild }) => {
                if (isLastRow) {
                  if (hasRequestedPastHistory.current) {
                    return (
                      <ArchiveStorySpinnerContainer key="archiveStorySpinner" ref={registerChild} style={style}>
                        <Spinner />
                      </ArchiveStorySpinnerContainer>
                    );
                  } else {
                    return (
                      <div key="endOfNewsfeed" ref={registerChild} style={style}>
                        <NewsfeedEndSpan>{endMessage.message}</NewsfeedEndSpan>
                      </div>
                    );
                  }
                } else if (isFirstRow) {
                  if (hasRequestedFutureHistory.current) {
                    return (
                      <ArchiveStorySpinnerContainer key="archiveStorySpinner" ref={registerChild} style={style}>
                        <Spinner />
                      </ArchiveStorySpinnerContainer>
                    );
                  } else {
                    return (
                      <div key="beginningOfNewsfeed" ref={registerChild} style={style}>
                        <NewsfeedEndSpan>{endMessage.message}</NewsfeedEndSpan>
                      </div>
                    );
                  }
                } else if (item === undefined)
                  return (
                    <div key="endOfNewsfeed" ref={registerChild} style={style}>
                      <NewsfeedEndSpan>{endMessage.message}</NewsfeedEndSpan>
                    </div>
                  );
                switch (item.type) {
                  case 'date':
                    return (
                      <div key={key} ref={registerChild} style={style}>
                        <DateRow>{item.displayDate}</DateRow>
                      </div>
                    );
                  case 'story':
                    return (
                      <div key={key} ref={registerChild} style={style}>
                        <NewsfeedStoryContainer
                          computeHeight={measure}
                          displayType={internalSettings.displayType}
                          narrowWidthLayout={narrowWidthLayout.current}
                          paddingStyle={paddingStyle}
                          positionStyles={{}}
                          story={item.story}
                          textSize={globalSettings.textSize}
                          widgetID={widgetID}
                          widgetWidth={widgetWidth || 550}
                        />
                      </div>
                    );
                }
              }}
            </CellMeasurer>
          );
        } else {
          return null;
        }
      },
      [
        newsfeedContainer,
        displayFirstDate,
        currentCache,
        endMessage.message,
        internalSettings.displayType,
        paddingStyle,
        globalSettings.textSize,
        widgetID,
        widgetWidth,
      ],
    );

    const renderNoResults = React.useCallback(() => {
      const message = 'No results found for Expression.';
      return <NoResults message={message} />;
    }, []);

    const isRowLoaded = React.useCallback(
      ({ index }: { index: number }): boolean => {
        if (index === 0 && !noMoreFutureHistory.current) {
          return hasRequestedFutureHistory.current;
        }
        return (
          noMorePastHistory.current === false &&
          index < (newsfeedContainer?.getDisplayItemsFormattedLength(displayFirstDate) ?? 0)
        );
      },
      [displayFirstDate, newsfeedContainer],
    );

    const loadMoreRows = React.useCallback<React.ComponentProps<typeof InfiniteLoader>['loadMoreRows']>(
      async (indexRange): Promise<undefined> => {
        if (endMessage.state === 'connected') {
          if (!noMoreFutureHistory.current && indexRange.startIndex === 0) {
            hasRequestedFutureHistory.current = true;
            await newsfeedContainer?.getHistoric(historicLimit, 'future');
          } else if (noMorePastHistory.current === false) {
            hasRequestedPastHistory.current = true;
            await newsfeedContainer?.getHistoric(historicLimit);
          }
        }
        return undefined;
      },
      [endMessage.state, historicLimit, newsfeedContainer],
    );

    const onScrolling = React.useCallback(
      (params: { scrollTop: number }) => {
        if (params.scrollTop === 0 && noMoreFutureHistory.current) {
          newsfeedContainer?.resumeLive();
        } else {
          newsfeedContainer?.pauseLive();
        }

        const setBannerDate = internalSettings.setBannerDate;
        const idx = indexFromScrollTop(params.scrollTop - 30);
        const buff = newsfeedContainer?.getDisplayItemsFormatted(displayFirstDate);
        const buffLen = newsfeedContainer?.getDisplayItemsFormattedLength(displayFirstDate);
        if (!buff || !buffLen || buffLen < idx) {
          setBannerDate?.(null);
          return;
        }
        const scrolledElement = buff[buffLen - idx - 1];
        setBannerDate?.(dateStringFromItem(scrolledElement));
      },
      [dateStringFromItem, displayFirstDate, indexFromScrollTop, internalSettings.setBannerDate, newsfeedContainer],
    );

    const recomputeRowHeights = React.useCallback(() => {
      cache.current = createCache();
      update();
    }, [createCache, update]);

    const timeoutHandle = React.useRef<ReturnType<typeof setTimeout> | undefined>(undefined);
    const onResize = React.useCallback(
      (props: { height: number; width: number }) => {
        if (prevWidth.current && prevWidth.current !== props.width) {
          if (timeoutHandle.current) {
            clearTimeout(timeoutHandle.current);
          }
          timeoutHandle.current = setTimeout(() => {
            recomputeRowHeights();
            timeoutHandle.current = undefined;
          }, 2000);
        }
        prevWidth.current = props.width;
      },
      [recomputeRowHeights],
    );

    const localOnRowsRendered = React.useCallback<NonNullable<React.ComponentProps<typeof List>['onRowsRendered']>>(
      info => {
        lastScrolledPosition.current = info.startIndex;
      },
      [],
    );

    const onVisible = React.useCallback(
      (isVisible: boolean) => {
        if (isVisible) {
          recomputeRowHeights();
        }
      },
      [recomputeRowHeights],
    );

    Hooks.useOnVisibilityChange(props.parentElement, onVisible); // this is a hack to force reload on workspace change

    React.useEffect(() => {
      recomputeRowHeights();
    }, [globalSettings, recomputeRowHeights]);

    const { displayType } = internalSettings;
    const { headerSpacing, textSize } = globalSettings;
    // We add 1 to the amount of rowIds to render the spinner/end of newsfeed prompt as the last row.
    const rowCount = (newsfeedContainer?.getDisplayItemsFormattedLength(displayFirstDate) ?? 0) + 1;

    if (newsfeedContainer?.getDisplayItemsFormattedLength(displayFirstDate) === 0 && hasRequestedPastHistory.current) {
      resetFirstScrolledToItem();
      return (
        <MainSpinnerContainer>
          <Spinner />
        </MainSpinnerContainer>
      );
    }

    return (
      <InfiniteLoader
        isRowLoaded={isRowLoaded}
        loadMoreRows={loadMoreRows}
        minimumBatchSize={100}
        rowCount={rowCount}
        threshold={10}
      >
        {({ onRowsRendered, registerChild: childList }) => (
          <WindowScroller onResize={onResize} onScroll={onScrolling} scrollElement={props.parentElement}>
            {({ height, isScrolling, onChildScroll, scrollTop, width }) =>
              width ? (
                <List
                  autoHeight
                  deferredMeasurementCache={currentCache}
                  displayType={displayType}
                  estimatedRowSize={75}
                  headerSpacing={headerSpacing}
                  height={height ?? 0}
                  isScrolling={isScrolling}
                  noRowsRenderer={renderNoResults as () => JSX.Element}
                  onRowsRendered={(...args) => {
                    localOnRowsRendered(...args);
                    onRowsRendered(...args);
                    onRowsRenderedHandle();
                  }}
                  onScroll={onChildScroll}
                  overscanRowCount={20}
                  ref={childList}
                  rowCount={rowCount}
                  rowHeight={currentCache.rowHeight}
                  rowRenderer={rowRenderer}
                  scrollToAlignment="start"
                  scrollToIndex={setScroll.current != null ? setScroll.current : undefined}
                  scrollTop={setScroll.current != null ? undefined : scrollTop}
                  // These are passthrough props. (They trigger the List to rerender when they change).
                  textSize={textSize}
                  width={width}
                />
              ) : null
            }
          </WindowScroller>
        )}
      </InfiniteLoader>
    );
  }),
);

const MainSpinnerContainer = styled(TC.Div)`
  background-color: ${props => props.theme.colors.background};
  bottom: 0;
  height: 26px;
  left: 0;
  margin: auto;
  position: absolute;
  right: 0;
  top: 0;
  width: 25px;
`;

const NewsfeedEndSpan = styled(TC.Span)`
  background-color: ${props => props.theme.colors.backgroundActive};
  display: flex;
  flex: 1;
  font-weight: 500;
  justify-content: center;
  line-height: 1.3em;
  margin: 10px auto;
  padding: 0.5em 1em;
  text-align: center;
  width: fit-content;
`;

export const DateRow = styled(TC.Div)`
  border-bottom: 1px solid ${props => props.theme.colors.backgroundActive};
  box-sizing: border-box;
  font-weight: 600;
  line-height: 20px;
  padding: 2px 8px;
`;

const ArchiveStorySpinnerContainer = styled(TC.Row)`
  height: 36px !important;
  padding-left: 50%;
  padding-right: 50%;
`;
