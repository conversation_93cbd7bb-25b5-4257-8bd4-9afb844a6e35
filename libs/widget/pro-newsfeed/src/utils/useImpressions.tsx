import React from 'react';
import { isGlobalImpressionStored, storeGlobalImpression } from '@benzinga/content-manager';
import { SessionContext } from '@benzinga/session-context';
import { TrackingManager } from '@benzinga/tracking-manager';

interface Props {
  widgetID?: string;
  nodeID?: string | number;
}

export const useHeadlineImpression = ({ widgetID }: Props) => {
  const session = React.useContext(SessionContext);

  const trackHeadlineImpression = React.useCallback(
    (nodeID: string) => {
      const impressionSlug = `${widgetID}-${nodeID}`;

      if (!isGlobalImpressionStored(impressionSlug)) {
        storeGlobalImpression(impressionSlug);
        session.getManager(TrackingManager).trackHeadlineEvent('viewed_inline', {
          nodeID,
        });
      }
    },
    [widgetID, session],
  );

  const trackClickEvent = React.useCallback(
    (nodeID: string) => {
      session.getManager(TrackingManager).trackHeadlineEvent('clicked_inline', {
        nodeID,
      });
    },
    [session],
  );

  return { trackClickEvent, trackHeadlineImpression };
};
