import { useCallback, useEffect, useRef } from 'react';
import { useHeadlineImpression } from './useImpressions';

interface Props {
  parentElement?: Element;
  widgetID?: string;
}

const NODE_ID_ATTRIBUTE = 'node-id';

const VIRTUAL_GRID_INNER_CONTAINER_SELECTOR = '.ReactVirtualized__Grid__innerScrollContainer';

export const useNewsfeedTable = ({ parentElement, widgetID }: Props) => {
  const { trackHeadlineImpression } = useHeadlineImpression({ widgetID });
  const virtualGridInnerContainer = useRef<Element | null | undefined>(null);

  const trackNodeID = useCallback(
    (event: Event) => {
      const target = event.target as Element | null;
      const nodeID = target?.getAttribute?.(`data-${NODE_ID_ATTRIBUTE}`);
      if (nodeID) {
        trackHeadlineImpression(nodeID);
      }
    },
    [trackHeadlineImpression],
  );

  const resetListener = useCallback(() => {
    if (virtualGridInnerContainer.current) {
      virtualGridInnerContainer.current?.removeEventListener('mouseover', trackNodeID);
      virtualGridInnerContainer.current = null;
    }
  }, [trackNodeID]);

  const subscribeOnMouseOverEvents = useCallback(
    callback => {
      const innerContainer = parentElement?.querySelector(VIRTUAL_GRID_INNER_CONTAINER_SELECTOR);
      resetListener();

      innerContainer?.addEventListener('mouseover', callback);
      virtualGridInnerContainer.current = innerContainer;
    },
    [parentElement, resetListener],
  );

  useEffect(() => {
    return () => {
      resetListener();
    };
  }, [trackNodeID, resetListener]);

  const onRowsRenderedHandle = () => {
    subscribeOnMouseOverEvents(trackNodeID);
  };

  return {
    onRowsRenderedHandle,
  };
};
