import { DefaultTableParameters } from '@benzinga/ag-grid-utils';

export const DEFAULT_WATCHLIST_CONFIG = {
  flightMode: false,
  sendGroup: null,
  table: {
    ...DefaultTableParameters,
    columns: [
      {
        colId: 'symbol',
        hide: false,
        pinned: 'left' as const,
        width: 100,
      },
      {
        colId: 'name',
        hide: false,
        width: 100,
      },
      {
        colId: 'price',
        hide: false,
        width: 100,
      },
      {
        colId: 'quantity',
        hide: false,
        width: 100,
      },
      {
        colId: 'sparkline0',
        hide: false,
        width: 100,
      },
      {
        colId: 'open',
        hide: false,
        width: 100,
      },
      {
        colId: 'regularHoursChange',
        hide: false,
        width: 100,
      },
      {
        colId: 'regularHoursPercentChange',
        hide: false,
        width: 100,
      },
      {
        colId: 'change',
        hide: false,
        width: 100,
      },
      {
        advancedSort: {
          val: 'desc' as const,
        },
        colId: 'changePercent',
        hide: false,
        sort: 'desc' as const,
        width: 100,
      },
      {
        colId: 'postToPreChange',
        hide: false,
        width: 100,
      },
      {
        colId: 'postToPrePercentChange',
        hide: false,
        width: 100,
      },
      {
        colId: 'high',
        hide: false,
        width: 100,
      },
      {
        colId: 'low',
        hide: false,
        width: 100,
      },
      {
        colId: 'dayVolume',
        hide: false,
        width: 100,
      },
      {
        colId: 'marketCap',
        hide: false,
        width: 100,
      },
      {
        colId: 'PriceAlerts',
        hide: false,
        width: 100,
      },
      {
        colId: 'notes',
        hide: false,
        width: 100,
      },
    ],
  },
  watchlistId: null,
};
