'use client';
import React from 'react';
import { useAnalystRatings } from '@benzinga/calendar-manager-hooks';
import { useAutoCompleteSymbols } from '@benzinga/pro-ui';
import dayjs from 'dayjs';
import type { EChartsOption } from 'echarts-for-react';
import { GaugeVisualization } from '../entites';
import { VisualizationArgs } from '@benzinga/visualization-utils';
import { ThemeContext } from '@benzinga/themetron';
import { hexToRGBA } from '@benzinga/utils';
import styled from 'styled-components';

export interface ExtendedGaugeVisualization {
  symbols: string[];
  mode: 'multiple' | 'combined';
}

const ReactECharts = React.lazy(() => import('echarts-for-react'));

const GuageAverageAnalystRatingsInternal: React.FC<
  VisualizationArgs<GaugeVisualization> & ExtendedGaugeVisualization
> = props => {
  const theme = React.useContext(ThemeContext);
  const dateTo = React.useMemo(() => dayjs().format('YYYY-MM-DD'), []);
  const dateFrom = React.useMemo(() => dayjs().subtract(3, 'month').format('YYYY-MM-DD'), []);
  const ratingsQuery = React.useMemo(
    () => ({
      dateFrom,
      dateTo,
      symbols: props.symbols,
    }),
    [dateFrom, dateTo, props.symbols],
  );
  const ratings = useAnalystRatings(ratingsQuery);
  let totalCountedRatings = 0;
  const avgRatings =
    ratings.reduce((totalRatings, currentRating) => {
      switch (currentRating.rating_current) {
        case 'Buy':
          totalCountedRatings++;
          return totalRatings + 1;
        case 'Sell':
          totalCountedRatings++;
          return totalRatings - 1;
        case 'Overweight':
        case 'Outperform':
          totalCountedRatings++;
          return totalRatings + 0.5;
        case 'Underperform':
        case 'Underweight':
          totalCountedRatings++;
          return totalRatings - 0.5;
        case 'Neutral':
        case 'Perform':
        case 'Hold':
        case 'Equal-Weight':
          totalCountedRatings++;
          return totalRatings;
        default:
          return totalRatings;
      }
    }, 0) / totalCountedRatings;

  const options: EChartsOption = React.useMemo(
    () => ({
      series: [
        {
          axisLabel: {
            color: theme.colors.foreground,
            distance: props.mode === 'multiple' ? -40 : -28,
            fontSize: 'auto',
            formatter: function (value: number) {
              if (value === 0.96875) {
                return 'Buy';
              } else if (value === 0.5) {
                return 'Hold';
              } else if (value === 0.03125) {
                return 'Sell';
              }
              return '';
            },
            rotate: 'tangential',
          },
          axisLine: {
            lineStyle: {
              color:
                theme.name === 'light'
                  ? [
                      [0.2, theme.colors.statistic.negative],
                      [0.4, hexToRGBA(theme.colors.statistic.negative, 0.8)],
                      [0.6, theme.colors.foregroundInactive],
                      [0.8, hexToRGBA(theme.colors.statistic.positive, 0.8)],
                      [1, theme.colors.statistic.positive],
                    ]
                  : [
                      [0.2, theme.colors.statistic.negative],
                      [0.4, '#ff887f'],
                      [0.6, theme.colors.foregroundInactive],
                      [0.8, '#80ffe5'],
                      [1, theme.colors.statistic.positive],
                    ],
              width: 10,
            },
          },
          axisTick: {
            show: false,
          },
          center: ['50%', '70%'],
          data: [
            {
              name: 'Grade Rating',
              value: avgRatings / 2 + 0.5,
            },
          ],
          detail: {
            color: 'inherit',
            fontSize: 20,
            formatter: function (value: number) {
              return !isNaN(value) ? Math.round(value * 1000) / 10 + '' : 'None';
            },
            offsetCenter: [0, '-35%'],
            valueAnimation: true,
          },
          emphasis: {
            disabled: true,
          },
          endAngle: 0,
          max: 1,
          min: 0,
          pointer: {
            icon: 'path://M12.8,0.7l12,40.1H0.7L12.8,0.7z',
            itemStyle: {
              color: 'inherit',
            },
            length: '12%',
            offsetCenter: [0, '-70%'],
            width: 20,
          },
          radius: props.mode === 'multiple' ? '100%' : '84%',
          splitLine: {
            show: false,
          },
          splitNumber: 32,
          startAngle: 180,
          title: {
            color: 'white',
            fontSize: 15,
            offsetCenter: [0, '0%'],
          },
          type: 'gauge',
        },
      ],
    }),
    [
      avgRatings,
      props.mode,
      theme.colors.foreground,
      theme.colors.foregroundInactive,
      theme.colors.statistic.negative,
      theme.colors.statistic.positive,
      theme.name,
    ],
  );

  return (
    <ChartWrapper mode={props.mode}>
      {props.mode === 'multiple' && <ChartTitle>{props.symbols[0]}</ChartTitle>}
      <ReactECharts notMerge={true} option={options} style={{ height: '100%', width: '100%' }} />
    </ChartWrapper>
  );
};

export const GuageAverageAnalystRatings: React.FC<VisualizationArgs<GaugeVisualization>> = props => {
  const symbols = useAutoCompleteSymbols(props.sources) ?? [];
  const mode = React.useMemo(
    () => props.args.layout?.mode?.toLowerCase() ?? props.args.traces[0].mode?.toLowerCase(),
    [props.args.layout?.mode, props.args.traces],
  );

  return (
    <React.Suspense fallback={null}>
      {mode === 'multiple' ? (
        <ReactEChartsWrapper>
          {symbols.map(symbol => (
            <GuageAverageAnalystRatingsInternal {...props} mode={'multiple'} symbols={[symbol]} />
          ))}
        </ReactEChartsWrapper>
      ) : (
        <GuageAverageAnalystRatingsInternal {...props} mode={'combined'} symbols={symbols} />
      )}
    </React.Suspense>
  );
};

const ChartWrapper = styled.div<{ mode?: string }>`
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 100%;
  height: 100%;
  box-sizing: border-box;
  background-color: ${props => props.theme.colors.background};
  border: ${props => (props.mode === 'multiple' ? `1px solid ${props.theme.colors.border}` : 'none')};
  border-radius: 8px;
`;

export const ChartTitle = styled.h3`
  margin: 0;
  padding: 0.5rem;
  width: 100%;
  text-align: center;
  background-color: ${props => props.theme.colors.backgroundActive};
  color: ${props => props.theme.colors.foregroundActive};
  font-size: 1rem;
  font-weight: 600;
`;

const ReactEChartsWrapper = styled.div`
  display: grid;
  box-sizing: border-box;
  gap: 1rem;
  width: 100%;

  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));

  @media (min-width: 768px) {
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  }

  @media (min-width: 1200px) {
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  }

  & > * {
    min-height: 250px;
  }
`;
