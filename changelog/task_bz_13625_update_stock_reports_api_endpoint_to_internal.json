{"affectedProjects": ["bz", "bz-mobile", "india", "money", "pro", "proto", "@benzinga/root", "bz-e2e", "data-manager-article", "data-manager-stock-reports", "pro-e2e", "react-utils-data-hooks-content-manager", "ui-ads", "ui-alternative-investments", "ui-article", "ui-blocks", "ui-bz-onboarding", "ui-calendars", "ui-charts", "ui-entity", "ui-money", "ui-navigation", "ui-news", "ui-quotes", "ui-table", "ui-templates", "ui-ticker", "ui-trade-ideas", "ui-ui", "ui-widgets", "visualization-iqchart", "widget-pro-bz-chart", "widget-pro-details", "widget-pro-newsfeed", "widget-pro-notification", "widget-scanner", "widget-ticker-finder"], "description": "update stock reports api endpoint to internal", "epic": null, "issueNumber": "13625", "project": "BZ", "projects": ["bz", "bz-mobile", "india", "money", "pro", "proto", "@benzinga/root", "bz-e2e", "data-manager-article", "data-manager-stock-reports", "pro-e2e", "react-utils-data-hooks-content-manager", "ui-ads", "ui-alternative-investments", "ui-article", "ui-blocks", "ui-bz-onboarding", "ui-calendars", "ui-charts", "ui-entity", "ui-money", "ui-navigation", "ui-news", "ui-quotes", "ui-table", "ui-templates", "ui-ticker", "ui-trade-ideas", "ui-ui", "ui-widgets", "visualization-iqchart", "widget-pro-bz-chart", "widget-pro-details", "widget-pro-newsfeed", "widget-pro-notification", "widget-scanner", "widget-ticker-finder"], "type": "task", "updatedAt": "2025-07-31T17:22:39.511Z"}